/**
 * Database Lock Manager
 * Prevents SIGTERM by managing database access and preventing locks
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DatabaseLockManager {
    constructor() {
        this.activeConnections = new Map();
        this.connectionQueue = [];
        this.maxConcurrentConnections = 1; // SQLite works best with single connection
        this.isShuttingDown = false;
        this.lockTimeout = 30000; // 30 seconds
        this.dbPath = path.join(__dirname, '..', 'data', 'templar_payroll_auditor.db');
    }

    async getConnection(requestId = null) {
        return new Promise((resolve, reject) => {
            if (this.isShuttingDown) {
                reject(new Error('Database manager is shutting down'));
                return;
            }

            const id = requestId || `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            console.log(`[DB-LOCK-MANAGER] Connection request: ${id}`);

            // Check if we can provide immediate connection
            if (this.activeConnections.size < this.maxConcurrentConnections) {
                this.createConnection(id, resolve, reject);
            } else {
                // Queue the request
                console.log(`[DB-LOCK-MANAGER] Queuing connection request: ${id}`);
                this.connectionQueue.push({ id, resolve, reject, timestamp: Date.now() });

                // Set timeout for queued request
                setTimeout(() => {
                    const queueIndex = this.connectionQueue.findIndex(req => req.id === id);
                    if (queueIndex !== -1) {
                        this.connectionQueue.splice(queueIndex, 1);
                        reject(new Error(`Database connection timeout for ${id}`));
                    }
                }, this.lockTimeout);
            }
        });
    }

    createConnection(id, resolve, reject) {
        console.log(`[DB-LOCK-MANAGER] Creating connection: ${id}`);
        
        const db = new sqlite3.Database(this.dbPath, sqlite3.OPEN_READWRITE, (err) => {
            if (err) {
                console.error(`[DB-LOCK-MANAGER] Connection failed for ${id}:`, err.message);
                reject(err);
                return;
            }
            
            // Configure connection for better concurrency
            db.configure('busyTimeout', 10000); // 10 second busy timeout
            
            // Wrap database with cleanup
            const wrappedDb = {
                db: db,
                id: id,
                createdAt: Date.now(),
                
                // Wrap common methods
                get: (sql, params, callback) => {
                    return db.get(sql, params, callback);
                },
                
                all: (sql, params, callback) => {
                    return db.all(sql, params, callback);
                },
                
                run: (sql, params, callback) => {
                    return db.run(sql, params, callback);
                },
                
                // Custom close method
                close: () => {
                    return this.releaseConnection(id);
                }
            };
            
            this.activeConnections.set(id, wrappedDb);
            console.log(`[DB-LOCK-MANAGER] Connection created: ${id}`);
            resolve(wrappedDb);
        });
    }

    releaseConnection(id) {
        return new Promise((resolve) => {
            const connection = this.activeConnections.get(id);
            if (connection) {
                console.log(`[DB-LOCK-MANAGER] Releasing connection: ${id}`);
                
                connection.db.close((err) => {
                    if (err) {
                        console.error(`[DB-LOCK-MANAGER] Error closing connection ${id}:`, err.message);
                    }
                    
                    this.activeConnections.delete(id);
                    console.log(`[DB-LOCK-MANAGER] Connection released: ${id}`);
                    
                    // Process next queued request
                    this.processQueue();
                    resolve();
                });
            } else {
                console.log(`[DB-LOCK-MANAGER] Connection ${id} not found for release`);
                resolve();
            }
        });
    }

    processQueue() {
        if (this.connectionQueue.length > 0 && this.activeConnections.size < this.maxConcurrentConnections) {
            const nextRequest = this.connectionQueue.shift();
            console.log(`[DB-LOCK-MANAGER] Processing queued request: ${nextRequest.id}`);
            this.createConnection(nextRequest.id, nextRequest.resolve, nextRequest.reject);
        }
    }

    async executeQuery(sql, params = []) {
        const connection = await this.getConnection();
        
        return new Promise((resolve, reject) => {
            connection.all(sql, params, (err, rows) => {
                connection.close(); // Always close after query
                
                if (err) {
                    console.error('[DB-LOCK-MANAGER] Query error:', err.message);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async executeUpdate(sql, params = []) {
        const connection = await this.getConnection();
        
        return new Promise((resolve, reject) => {
            connection.run(sql, params, function(err) {
                connection.close(); // Always close after update
                
                if (err) {
                    console.error('[DB-LOCK-MANAGER] Update error:', err.message);
                    reject(err);
                } else {
                    resolve({ changes: this.changes, lastID: this.lastID });
                }
            });
        });
    }

    async closeAllConnections() {
        console.log('[DB-LOCK-MANAGER] Closing all connections...');
        this.isShuttingDown = true;

        const closePromises = Array.from(this.activeConnections.keys()).map(id =>
            this.releaseConnection(id)
        );

        await Promise.all(closePromises);

        // Clear queue
        this.connectionQueue.forEach(req => {
            req.reject(new Error('Database manager shutting down'));
        });
        this.connectionQueue = [];

        console.log('[DB-LOCK-MANAGER] All connections closed');
    }

    getStatus() {
        return {
            activeConnections: this.activeConnections.size,
            queuedRequests: this.connectionQueue.length,
            maxConcurrent: this.maxConcurrentConnections
        };
    }
}

// Global instance
let globalLockManager = null;

function getDatabaseLockManager() {
    if (!globalLockManager) {
        globalLockManager = new DatabaseLockManager();
    }
    return globalLockManager;
}

// Export for Electron environment
if (typeof window !== 'undefined') {
    // Electron renderer process - attach to window
    window.DatabaseLockManager = DatabaseLockManager;
    window.getDatabaseLockManager = getDatabaseLockManager;
    console.log('[DB-LOCK-MANAGER] Loaded in Electron renderer process');
}

// Only export for Node.js if we're actually in Node.js main process
if (typeof module !== 'undefined' && typeof window === 'undefined') {
    module.exports = { DatabaseLockManager, getDatabaseLockManager };
}
