/**
 * Safe Tracker Population
 * Populates Bank Adviser tables WITHOUT causing SIGTERM or database locks
 */

const { getDatabaseLockManager } = require('./database_lock_manager.js');

class SafeTrackerPopulation {
    constructor() {
        this.lockManager = getDatabaseLockManager();
        this.isPopulating = false;
    }

    async populateTrackerTables(sessionId = null) {
        if (this.isPopulating) {
            console.log('[SAFE-TRACKER] Already populating, skipping duplicate request');
            return { success: false, error: 'Population already in progress' };
        }

        this.isPopulating = true;
        
        try {
            console.log('[SAFE-TRACKER] Starting safe tracker population...');

            // Get current session if not provided
            if (!sessionId) {
                const sessionRows = await this.lockManager.executeQuery('SELECT session_id FROM current_session WHERE id = 1');
                if (!sessionRows || sessionRows.length === 0) {
                    return { success: false, error: 'No current session found' };
                }
                sessionId = sessionRows[0].session_id;
            }

            console.log(`[SAFE-TRACKER] Using session: ${sessionId}`);

            // Get tracker results
            const trackerData = await this.getTrackerResults(sessionId);
            if (!trackerData || trackerData.length === 0) {
                console.log('[SAFE-TRACKER] No tracker results found');
                return { 
                    success: true, 
                    message: 'No tracker data to populate',
                    in_house_loans: 0,
                    external_loans: 0,
                    motor_vehicles: 0,
                    total: 0
                };
            }

            console.log(`[SAFE-TRACKER] Found ${trackerData.length} tracker results`);

            // Clear existing data for this session
            await this.clearExistingData(sessionId);

            // Populate each table safely
            const results = await this.populateAllTables(sessionId, trackerData);

            console.log('[SAFE-TRACKER] Population completed successfully');
            return results;

        } catch (error) {
            console.error('[SAFE-TRACKER] Population failed:', error);
            return { success: false, error: error.message };

        } finally {
            this.isPopulating = false;
        }
    }

    async getTrackerResults(sessionId) {
        try {
            const query = `
                SELECT tracker_type, employee_id, employee_name, item_label,
                       item_value, numeric_value, created_at
                FROM tracker_results
                WHERE session_id = ?
                ORDER BY tracker_type, employee_id
            `;
            
            return await this.lockManager.executeQuery(query, [sessionId]);

        } catch (error) {
            console.error('[SAFE-TRACKER] Error getting tracker results:', error);
            return [];
        }
    }

    async clearExistingData(sessionId) {
        try {
            console.log('[SAFE-TRACKER] Clearing existing data...');

            const tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance'];
            
            for (const table of tables) {
                try {
                    await this.lockManager.executeUpdate(
                        `DELETE FROM ${table} WHERE source_session = ?`,
                        [sessionId]
                    );
                    console.log(`[SAFE-TRACKER] Cleared ${table}`);
                } catch (error) {
                    console.log(`[SAFE-TRACKER] Could not clear ${table}: ${error.message}`);
                }
            }

        } catch (error) {
            console.error('[SAFE-TRACKER] Error clearing data:', error);
        }
    }

    async populateAllTables(sessionId, trackerData) {
        const results = {
            success: true,
            in_house_loans: 0,
            external_loans: 0,
            motor_vehicles: 0,
            total: 0
        };

        try {
            // Group data by tracker type
            const groupedData = this.groupByTrackerType(trackerData);

            // Populate in_house_loans
            if (groupedData.in_house_loans) {
                results.in_house_loans = await this.populateInHouseLoans(sessionId, groupedData.in_house_loans);
            }

            // Populate external_loans
            if (groupedData.external_loans) {
                results.external_loans = await this.populateExternalLoans(sessionId, groupedData.external_loans);
            }

            // Populate motor_vehicles
            if (groupedData.motor_vehicles) {
                results.motor_vehicles = await this.populateMotorVehicles(sessionId, groupedData.motor_vehicles);
            }

            results.total = results.in_house_loans + results.external_loans + results.motor_vehicles;

            console.log(`[SAFE-TRACKER] Populated: ${results.in_house_loans} in-house, ${results.external_loans} external, ${results.motor_vehicles} vehicles`);

            return results;

        } catch (error) {
            console.error('[SAFE-TRACKER] Error populating tables:', error);
            return { success: false, error: error.message };
        }
    }

    groupByTrackerType(trackerData) {
        const grouped = {
            in_house_loans: [],
            external_loans: [],
            motor_vehicles: []
        };

        for (const item of trackerData) {
            const type = item.tracker_type;
            if (type === 'IN_HOUSE_LOAN') {
                grouped.in_house_loans.push(item);
            } else if (type === 'EXTERNAL_LOAN') {
                grouped.external_loans.push(item);
            } else if (type === 'MOTOR_VEHICLE') {
                grouped.motor_vehicles.push(item);
            }
        }

        return grouped;
    }

    async populateInHouseLoans(sessionId, loans) {
        let count = 0;
        
        for (const loan of loans) {
            try {
                await this.lockManager.executeUpdate(`
                    INSERT INTO in_house_loans (
                        employee_id, employee_name, loan_amount,
                        item_label, source_session
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    loan.employee_id,
                    loan.employee_name,
                    loan.numeric_value || 0,
                    loan.item_label || 'Unknown',
                    sessionId
                ]);
                count++;
            } catch (error) {
                console.error(`[SAFE-TRACKER] Error inserting in-house loan for ${loan.employee_id}:`, error);
            }
        }

        return count;
    }

    async populateExternalLoans(sessionId, loans) {
        let count = 0;
        
        for (const loan of loans) {
            try {
                await this.lockManager.executeUpdate(`
                    INSERT INTO external_loans (
                        employee_id, employee_name, loan_amount,
                        item_label, source_session
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    loan.employee_id,
                    loan.employee_name,
                    loan.numeric_value || 0,
                    loan.item_label || 'Unknown',
                    sessionId
                ]);
                count++;
            } catch (error) {
                console.error(`[SAFE-TRACKER] Error inserting external loan for ${loan.employee_id}:`, error);
            }
        }

        return count;
    }

    async populateMotorVehicles(sessionId, vehicles) {
        let count = 0;
        
        for (const vehicle of vehicles) {
            try {
                await this.lockManager.executeUpdate(`
                    INSERT INTO motor_vehicle_maintenance (
                        employee_id, employee_name, maintenance_amount,
                        item_label, source_session
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    vehicle.employee_id,
                    vehicle.employee_name,
                    vehicle.numeric_value || 0,
                    vehicle.item_label || 'Unknown',
                    sessionId
                ]);
                count++;
            } catch (error) {
                console.error(`[SAFE-TRACKER] Error inserting motor vehicle for ${vehicle.employee_id}:`, error);
            }
        }

        return count;
    }
}

// Global instance
let globalSafeTracker = null;

function getSafeTrackerPopulation() {
    if (!globalSafeTracker) {
        globalSafeTracker = new SafeTrackerPopulation();
    }
    return globalSafeTracker;
}

module.exports = { SafeTrackerPopulation, getSafeTrackerPopulation };
