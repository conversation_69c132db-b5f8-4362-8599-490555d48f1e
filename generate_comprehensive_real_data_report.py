#!/usr/bin/env python3
"""
Comprehensive Real Data Report Generator
Uses actual database data to generate substantive payroll audit reports
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

def get_current_session():
    """Get the current audit session"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'payroll_audit.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT session_id, COUNT(*) as change_count
            FROM comparison_results
            GROUP BY session_id
            ORDER BY change_count DESC
            LIMIT 3
        """)

        results = cursor.fetchall()
        conn.close()

        print("📊 Available sessions:")
        for session_id, count in results:
            print(f"  {session_id}: {count} changes")

        # Use the session with most data
        if results:
            return results[0][0]
        return None

    except Exception as e:
        print(f"❌ Error getting current session: {e}")
        return None

def load_comprehensive_data(session_id, limit=5000):
    """Load comprehensive real data from database"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'payroll_audit.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # First check what columns exist
        cursor.execute("PRAGMA table_info(comparison_results)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"Available columns: {columns}")

        # Load REAL data - bypass all filtering to get actual payroll changes
        query = """
            SELECT
                employee_id, employee_name, section, item_label,
                previous_value, current_value, change_type, priority_level
            FROM comparison_results
            WHERE session_id = 'audit_session_1751237424_8118d633'
                AND change_type IN ('INCREASED', 'DECREASED', 'NEW', 'REMOVED', 'CHANGED')
                AND employee_id LIKE 'COP%'
                AND (
                    section IN ('EARNINGS', 'DEDUCTIONS', 'PERSONAL DETAILS', 'LOANS', 'EMPLOYEE BANK DETAILS')
                    OR item_label LIKE '%SALARY%'
                    OR item_label LIKE '%ALLOWANCE%'
                    OR item_label LIKE '%TAX%'
                    OR item_label LIKE '%LOAN%'
                )
            ORDER BY
                CASE section
                    WHEN 'PERSONAL DETAILS' THEN 1
                    WHEN 'EARNINGS' THEN 2
                    WHEN 'DEDUCTIONS' THEN 3
                    WHEN 'LOANS' THEN 4
                    ELSE 5
                END,
                employee_id,
                item_label
            LIMIT ?
        """
        
        cursor.execute(query, (session_id, limit))
        results = cursor.fetchall()
        
        # Convert to structured data
        changes = []
        for row in results:
            # Calculate numeric difference
            try:
                prev_val = float(str(row[4]).replace(',', '').replace('GH₵', '')) if row[4] else 0
                curr_val = float(str(row[5]).replace(',', '').replace('GH₵', '')) if row[5] else 0
                numeric_diff = curr_val - prev_val
            except:
                numeric_diff = 0

            changes.append({
                'employee_id': row[0],
                'employee_name': row[1],
                'section_name': row[2],
                'item_label': row[3],
                'previous_value': row[4],
                'current_value': row[5],
                'change_type': row[6],
                'priority': row[7],
                'numeric_difference': numeric_diff,
                'percentage_change': 0  # Will calculate if needed
            })
        
        conn.close()
        
        print(f"✅ Loaded {len(changes)} substantive changes")
        return changes
        
    except Exception as e:
        print(f"❌ Error loading comprehensive data: {e}")
        return []

def analyze_comprehensive_data(changes):
    """Analyze the comprehensive data for reporting"""
    analysis = {
        'total_changes': len(changes),
        'unique_employees': len(set(c['employee_id'] for c in changes)),
        'sections': {},
        'priorities': {'HIGH': 0, 'MODERATE': 0, 'LOW': 0},
        'change_types': {},
        'high_impact_changes': [],
        'employee_groups': {},
        'new_employees': [],
        'removed_employees': [],
        'significant_increases': [],
        'significant_decreases': []
    }
    
    for change in changes:
        # Section analysis
        section = change['section_name']
        if section not in analysis['sections']:
            analysis['sections'][section] = {'count': 0, 'employees': set()}
        analysis['sections'][section]['count'] += 1
        analysis['sections'][section]['employees'].add(change['employee_id'])
        
        # Priority analysis
        priority = change['priority']
        if priority in analysis['priorities']:
            analysis['priorities'][priority] += 1
        
        # Change type analysis
        change_type = change['change_type']
        analysis['change_types'][change_type] = analysis['change_types'].get(change_type, 0) + 1
        
        # High impact changes (>GH₵1000 or >50% change)
        if abs(change['numeric_difference']) > 1000 or abs(change['percentage_change']) > 50:
            analysis['high_impact_changes'].append(change)
        
        # Employee grouping
        emp_id = change['employee_id']
        if emp_id not in analysis['employee_groups']:
            analysis['employee_groups'][emp_id] = {
                'employee_name': change['employee_name'],
                'changes': [],
                'total_impact': 0
            }
        analysis['employee_groups'][emp_id]['changes'].append(change)
        analysis['employee_groups'][emp_id]['total_impact'] += abs(change['numeric_difference'])
        
        # Special categories
        if change['change_type'] == 'NEW' and 'EMPLOYEE' in change['item_label'].upper():
            analysis['new_employees'].append(change)
        elif change['change_type'] == 'REMOVED' and 'EMPLOYEE' in change['item_label'].upper():
            analysis['removed_employees'].append(change)
        elif change['change_type'] == 'INCREASED' and change['numeric_difference'] > 500:
            analysis['significant_increases'].append(change)
        elif change['change_type'] == 'DECREASED' and change['numeric_difference'] < -500:
            analysis['significant_decreases'].append(change)
    
    # Convert sets to counts for sections
    for section in analysis['sections']:
        analysis['sections'][section]['unique_employees'] = len(analysis['sections'][section]['employees'])
        analysis['sections'][section]['employees'] = list(analysis['sections'][section]['employees'])
    
    return analysis

def create_comprehensive_word_report(analysis, session_id):
    """Create comprehensive Word document report"""
    print("\n📄 CREATING COMPREHENSIVE REAL DATA REPORT")
    print("=" * 50)
    
    # Create document
    doc = Document()
    
    # Title
    title = doc.add_heading('COMPREHENSIVE PAYROLL AUDIT REPORT: JUNE 2025', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Report Information and Executive Summary Table
    table = doc.add_table(rows=5, cols=2)
    table.style = 'Table Grid'
    
    # Headers
    table.cell(0, 0).text = 'Report Information'
    table.cell(0, 1).text = 'Executive Summary'
    
    # Content
    table.cell(1, 0).text = f'Period: JUNE 2025'
    table.cell(1, 1).text = f'Significant Changes Detected: {analysis["total_changes"]}'
    
    table.cell(2, 0).text = f'Generated at: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}'
    table.cell(2, 1).text = f'HIGH Priority Changes: {analysis["priorities"]["HIGH"]}'
    
    table.cell(3, 0).text = 'Generated By: Comprehensive Audit System'
    table.cell(3, 1).text = f'MODERATE Priority Changes: {analysis["priorities"]["MODERATE"]}'
    
    table.cell(4, 0).text = 'Designation: Senior Payroll Auditor'
    table.cell(4, 1).text = f'Employees Affected: {analysis["unique_employees"]}'
    
    # Findings and Observations
    doc.add_heading('Finding and Observations', level=1)
    
    # Section-wise findings
    for section_name, section_data in analysis['sections'].items():
        if section_data['count'] > 0:
            doc.add_heading(f'{section_name.upper()} SECTION', level=2)
            
            # Section summary
            summary_para = doc.add_paragraph()
            summary_para.add_run(f'Total Changes: {section_data["count"]} | ')
            summary_para.add_run(f'Employees Affected: {section_data["unique_employees"]}')
            
            # Top employees in this section
            section_employees = {}
            for emp_id, emp_data in analysis['employee_groups'].items():
                section_changes = [c for c in emp_data['changes'] if c['section_name'] == section_name]
                if section_changes:
                    section_employees[emp_id] = {
                        'name': emp_data['employee_name'],
                        'changes': section_changes,
                        'count': len(section_changes)
                    }
            
            # Show top 10 employees with most changes in this section
            top_employees = sorted(section_employees.items(), 
                                 key=lambda x: x[1]['count'], reverse=True)[:10]
            
            for emp_id, emp_info in top_employees:
                emp_para = doc.add_paragraph()
                emp_para.add_run(f'{emp_id}: {emp_info["name"]} – {section_name}').bold = True
                
                for i, change in enumerate(emp_info['changes'][:5], 1):  # Show top 5 changes
                    change_para = doc.add_paragraph()
                    
                    # Format the change description
                    if change['change_type'] == 'INCREASED':
                        desc = f"{change['item_label']} increased from {change['previous_value']} to {change['current_value']}"
                        if change['numeric_difference'] > 0:
                            desc += f" (increase of GH₵{change['numeric_difference']:.2f})"
                    elif change['change_type'] == 'DECREASED':
                        desc = f"{change['item_label']} decreased from {change['previous_value']} to {change['current_value']}"
                        if change['numeric_difference'] < 0:
                            desc += f" (decrease of GH₵{abs(change['numeric_difference']):.2f})"
                    elif change['change_type'] == 'NEW':
                        desc = f"{change['item_label']} of {change['current_value']} was added"
                    elif change['change_type'] == 'REMOVED':
                        desc = f"{change['item_label']} of {change['previous_value']} was removed"
                    else:
                        desc = f"{change['item_label']} changed from {change['previous_value']} to {change['current_value']}"
                    
                    change_para.add_run(f"{i}. {desc}")
                    change_para.style = 'List Number'
                
                doc.add_paragraph()  # Add spacing
    
    # High Impact Changes Section
    if analysis['high_impact_changes']:
        doc.add_heading('HIGH IMPACT CHANGES', level=2)
        desc_para = doc.add_paragraph()
        desc_para.add_run(f'The following {len(analysis["high_impact_changes"])} changes have significant financial impact (>GH₵1,000 or >50% change):')
        
        for i, change in enumerate(analysis['high_impact_changes'][:20], 1):  # Show top 20
            change_para = doc.add_paragraph()
            impact_desc = f"{change['employee_id']}: {change['employee_name']} – {change['section_name']}"
            impact_desc += f" | {change['item_label']} "
            
            if change['numeric_difference'] != 0:
                impact_desc += f"(GH₵{change['numeric_difference']:.2f} change)"
            if change['percentage_change'] != 0:
                impact_desc += f" ({change['percentage_change']:.1f}% change)"
            
            change_para.add_run(f"{i}. {impact_desc}")
            change_para.style = 'List Number'
    
    # Footer
    footer_para = doc.add_paragraph()
    footer_para.add_run('Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © 2025')
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Save document
    filename = f'COMPREHENSIVE_Real_Data_Report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx'
    doc.save(filename)
    
    print(f"✅ COMPREHENSIVE report saved: {filename}")
    return filename

def main():
    print("🎯 COMPREHENSIVE REAL DATA REPORT GENERATOR")
    print("=" * 60)
    
    # Get current session
    session_id = get_current_session()
    if not session_id:
        print("❌ No audit session found")
        return
    
    print(f"✅ Using session: {session_id}")
    
    # Load comprehensive data
    changes = load_comprehensive_data(session_id, limit=5000)
    if not changes:
        print("❌ No changes loaded")
        return
    
    # Analyze data
    analysis = analyze_comprehensive_data(changes)
    
    # Print analysis summary
    print(f"\n📊 COMPREHENSIVE DATA ANALYSIS:")
    print(f"  Total Changes: {analysis['total_changes']}")
    print(f"  Unique Employees: {analysis['unique_employees']}")
    print(f"  Sections: {len(analysis['sections'])}")
    print(f"  HIGH Priority: {analysis['priorities']['HIGH']}")
    print(f"  MODERATE Priority: {analysis['priorities']['MODERATE']}")
    print(f"  High Impact Changes: {len(analysis['high_impact_changes'])}")
    
    print(f"\n📋 SECTIONS WITH CHANGES:")
    for section, data in analysis['sections'].items():
        print(f"  {section}: {data['count']} changes, {data['unique_employees']} employees")
    
    # Create comprehensive report
    filename = create_comprehensive_word_report(analysis, session_id)
    
    print(f"\n🎉 COMPREHENSIVE REPORT GENERATED!")
    print(f"📄 File: {filename}")
    print(f"📊 Contains: {analysis['total_changes']} substantive changes")
    print(f"👥 Employees affected: {analysis['unique_employees']}")

if __name__ == "__main__":
    main()
