#!/usr/bin/env python3
"""Check session status after job run completion"""

import sqlite3
from datetime import datetime

def check_sessions_after_run():
    """Check how many sessions we have and their status after the job run"""
    print("🔍 CHECKING SESSIONS AFTER JOB RUN")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # 1. Check current session
        print("1. 📋 CURRENT SESSION STATUS:")
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_result = cursor.fetchone()
        
        if current_result:
            current_session = current_result[0]
            print(f"   Current session: {current_session}")
        else:
            print("   ❌ No current session found")
            current_session = None
        
        # 2. Check all recent sessions (last 2 hours)
        print("\n2. 🕐 ALL RECENT SESSIONS (Last 2 hours):")
        cursor.execute('''
            SELECT session_id, created_at, status,
                   (SELECT COUNT(*) FROM extracted_data WHERE session_id = a.session_id) as extracted,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = a.session_id) as comparison,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = a.session_id) as pre_reporting
            FROM audit_sessions a
            WHERE created_at >= datetime('now', '-2 hours')
            ORDER BY created_at DESC
        ''')
        
        recent_sessions = cursor.fetchall()
        
        if recent_sessions:
            print(f"   Found {len(recent_sessions)} sessions in the last 2 hours:")
            for i, session in enumerate(recent_sessions):
                session_id, created_at, status, extracted, comparison, pre_reporting = session
                is_current = session_id == current_session
                marker = "👉 CURRENT" if is_current else "  "
                
                print(f"\n   {marker} Session {i+1}: {session_id}")
                print(f"      Created: {created_at}")
                print(f"      Status: {status}")
                print(f"      Data: Extracted={extracted}, Comparison={comparison}, Pre-reporting={pre_reporting}")
                
                # Check if this session has complete data
                if extracted > 0 and comparison > 0 and pre_reporting > 0:
                    print(f"      ✅ COMPLETE DATA - UI should work with this session")
                elif extracted > 0 and comparison > 0:
                    print(f"      ⚠️ PARTIAL DATA - Has extraction and comparison, missing pre-reporting")
                elif extracted > 0:
                    print(f"      ⚠️ MINIMAL DATA - Only has extraction data")
                else:
                    print(f"      ❌ NO DATA - Empty session")
        else:
            print("   ❌ No recent sessions found")
        
        # 3. Check for sessions with same timestamp (duplicates)
        print("\n3. 🔍 CHECKING FOR DUPLICATE SESSIONS:")
        cursor.execute('''
            SELECT created_at, COUNT(*) as count, GROUP_CONCAT(session_id) as session_ids
            FROM audit_sessions
            WHERE created_at >= datetime('now', '-2 hours')
            GROUP BY created_at
            HAVING COUNT(*) > 1
            ORDER BY created_at DESC
        ''')
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"   ⚠️ Found {len(duplicates)} timestamps with multiple sessions:")
            for timestamp, count, session_ids in duplicates:
                print(f"      {timestamp}: {count} sessions")
                print(f"        Session IDs: {session_ids}")
        else:
            print("   ✅ No duplicate sessions found")
        
        # 4. Find the best session to use
        print("\n4. 🎯 FINDING BEST SESSION FOR UI:")
        
        best_session = None
        best_score = -1
        
        for session in recent_sessions:
            session_id, created_at, status, extracted, comparison, pre_reporting = session
            
            # Score sessions based on completeness
            score = 0
            if extracted > 0: score += 1
            if comparison > 0: score += 2
            if pre_reporting > 0: score += 4  # Pre-reporting is most important
            
            print(f"   Session {session_id[:20]}... Score: {score}")
            print(f"     E={extracted}, C={comparison}, P={pre_reporting}")
            
            if score > best_score:
                best_score = score
                best_session = session
        
        if best_session:
            session_id, created_at, status, extracted, comparison, pre_reporting = best_session
            print(f"\n   🏆 BEST SESSION: {session_id}")
            print(f"      Score: {best_score}/7")
            print(f"      Data: E={extracted}, C={comparison}, P={pre_reporting}")
            
            if current_session != session_id:
                print(f"\n   ⚠️ CURRENT SESSION IS NOT THE BEST SESSION!")
                print(f"      Current: {current_session}")
                print(f"      Best: {session_id}")
                print(f"      🔧 Need to switch current session pointer")
                return session_id, False  # Return best session and "not current" flag
            else:
                print(f"\n   ✅ CURRENT SESSION IS THE BEST SESSION")
                return session_id, True   # Return best session and "is current" flag
        else:
            print(f"\n   ❌ NO GOOD SESSIONS FOUND")
            return None, False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking sessions: {e}")
        return None, False

if __name__ == "__main__":
    best_session, is_current = check_sessions_after_run()
    
    if best_session and not is_current:
        print(f"\n🔧 RECOMMENDATION: Switch to session {best_session}")
    elif best_session and is_current:
        print(f"\n✅ CURRENT SESSION IS OPTIMAL")
    else:
        print(f"\n❌ NO USABLE SESSIONS FOUND - May need to run audit again")
