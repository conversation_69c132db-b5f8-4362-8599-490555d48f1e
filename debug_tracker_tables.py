#!/usr/bin/env python3
"""Debug why tracker tables are empty despite successful tracker feeding"""

import sqlite3

def debug_tracker_tables():
    """Debug tracker table population issues"""
    print("🔍 DEBUGGING TRACKER TABLES")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check all tracker-related tables
        tracker_tables = [
            'in_house_loans',
            'external_loans', 
            'motor_vehicle_maintenance',
            'tracker_results',
            'leave_claims',
            'educational_subsidy',
            'long_service_awards'
        ]
        
        print(f"\n🔍 CHECKING TRACKER TABLES FOR SESSION: {session_id}")
        
        for table in tracker_tables:
            try:
                # Check if table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if not cursor.fetchone():
                    print(f"   ❌ {table}: TABLE DOES NOT EXIST")
                    continue
                
                # Check total records
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                total = cursor.fetchone()[0]
                
                # Check session-specific records
                try:
                    cursor.execute(f'SELECT COUNT(*) FROM {table} WHERE source_session = ?', (session_id,))
                    session_count = cursor.fetchone()[0]
                except:
                    # Try different column names
                    try:
                        cursor.execute(f'SELECT COUNT(*) FROM {table} WHERE session_id = ?', (session_id,))
                        session_count = cursor.fetchone()[0]
                    except:
                        session_count = "N/A (no session column)"
                
                print(f"   📊 {table}: {total} total, {session_count} for session")
                
            except Exception as e:
                print(f"   ❌ {table}: ERROR - {e}")
        
        # Check what NEW items were found for tracking
        print(f"\n🔍 CHECKING NEW ITEMS FOR TRACKING:")
        
        cursor.execute('''
            SELECT cr.section_name, cr.item_label, cr.change_type, COUNT(*) as count
            FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND cr.change_type = 'NEW'
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
              AND (di.include_new = 1 OR di.include_new IS NULL)
            GROUP BY cr.section_name, cr.item_label, cr.change_type
            ORDER BY count DESC
            LIMIT 20
        ''', (session_id,))
        
        new_items = cursor.fetchall()
        if new_items:
            print("   Top NEW items found:")
            for row in new_items:
                print(f"     {row[0]}.{row[1]} ({row[2]}) - {row[3]} records")
        else:
            print("   ❌ No NEW items found for tracking")
        
        # Check what items might be trackable
        print(f"\n🔍 CHECKING POTENTIALLY TRACKABLE ITEMS:")
        
        cursor.execute('''
            SELECT DISTINCT section_name, item_label, change_type
            FROM comparison_results 
            WHERE session_id = ? AND (
                section_name LIKE '%LOAN%' OR
                item_label LIKE '%LOAN%' OR
                item_label LIKE '%ADVANCE%' OR
                item_label LIKE '%MOTOR%' OR
                item_label LIKE '%VEHICLE%'
            )
            ORDER BY section_name, item_label
            LIMIT 20
        ''', (session_id,))
        
        trackable = cursor.fetchall()
        if trackable:
            print("   Potentially trackable items:")
            for row in trackable:
                print(f"     {row[0]}.{row[1]} ({row[2]})")
        else:
            print("   ❌ No potentially trackable items found")
        
        # Check tracker feeding phase status
        print(f"\n🔍 CHECKING TRACKER_FEEDING PHASE STATUS:")
        
        cursor.execute('''
            SELECT status, data_count, started_at, completed_at
            FROM session_phases 
            WHERE session_id = ? AND phase_name = 'TRACKER_FEEDING'
        ''', (session_id,))
        
        phase_status = cursor.fetchone()
        if phase_status:
            print(f"   Status: {phase_status[0]}")
            print(f"   Data count: {phase_status[1]}")
            print(f"   Started: {phase_status[2]}")
            print(f"   Completed: {phase_status[3]}")
        else:
            print("   ❌ No TRACKER_FEEDING phase record found")
        
        conn.close()
        
        print(f"\n💡 ANALYSIS:")
        print("1. Terminal showed: 'Tracker feeding completed: 62 items tracked'")
        print("2. But tracker tables appear empty")
        print("3. Possible issues:")
        print("   - Items tracked to wrong table")
        print("   - Wrong session_id column name")
        print("   - Tracker storage method not working")
        print("   - Items filtered out by dictionary settings")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_tracker_tables()
