#!/usr/bin/env python3
"""Fix the corrupted session by switching to the previous working session"""

import sqlite3

def fix_corrupted_session():
    """Fix the corrupted session issue"""
    print("🔧 FIXING CORRUPTED SESSION")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # The previous working session we identified earlier
        working_session = 'audit_session_1751218345_cb7183d3'
        
        print(f"🔄 Switching back to working session: {working_session}")
        
        # Check if the working session still has data
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (working_session,))
        pre_reporting_count = cursor.fetchone()[0]
        
        if pre_reporting_count > 0:
            print(f"✅ Working session has {pre_reporting_count} pre-reporting records")
            
            # Update current session pointer
            cursor.execute('''
                UPDATE current_session 
                SET session_id = ?, updated_at = datetime('now') 
                WHERE id = 1
            ''', (working_session,))
            
            # Update session status
            cursor.execute('''
                UPDATE audit_sessions 
                SET status = 'pre_reporting_ready' 
                WHERE session_id = ?
            ''', (working_session,))
            
            conn.commit()
            
            print(f"✅ Current session updated to: {working_session}")
            print(f"✅ Session status set to: pre_reporting_ready")
            
            # Clean up the corrupted session data
            corrupted_session = 'audit_session_1751224384_9bbc550b'
            print(f"\n🧹 Cleaning up corrupted session: {corrupted_session}")
            
            # Remove orphaned pre-reporting data from corrupted session
            cursor.execute('DELETE FROM pre_reporting_results WHERE session_id = ?', (corrupted_session,))
            deleted_count = cursor.rowcount
            
            # Update current_session if it was pointing to corrupted session
            cursor.execute('DELETE FROM current_session WHERE session_id = ?', (corrupted_session,))
            
            conn.commit()
            
            print(f"✅ Removed {deleted_count} orphaned pre-reporting records")
            print(f"✅ Cleaned up corrupted session references")
            
        else:
            print(f"❌ Working session no longer has data")
            print(f"   Need to run a fresh audit")
            
        # Verify the fix
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (current,))
        count = cursor.fetchone()[0]
        
        print(f"\n📋 VERIFICATION:")
        print(f"✅ Current session: {current}")
        print(f"✅ Pre-reporting records: {count}")
        
        if count > 0:
            print(f"🎉 SUCCESS: UI should now work!")
            print(f"💡 Refresh your app and the pre-reporting interface should load")
        else:
            print(f"❌ Still no data - may need to run audit again")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error fixing session: {e}")

if __name__ == "__main__":
    fix_corrupted_session()
