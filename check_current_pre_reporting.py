#!/usr/bin/env python3
"""Check current session pre_reporting_results"""

import sqlite3

def check_current_pre_reporting():
    """Check current session pre_reporting_results structure"""
    print("🔍 CHECKING CURRENT SESSION PRE_REPORTING_RESULTS")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check table schema
        cursor.execute("PRAGMA table_info(pre_reporting_results)")
        columns = cursor.fetchall()
        
        print(f"\n📋 TABLE SCHEMA:")
        column_names = []
        for col in columns:
            col_id, name, data_type, not_null, default, pk = col
            column_names.append(name)
            print(f"   {name} ({data_type})")
        
        # Check if current session has data
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session_id,))
        count = cursor.fetchone()[0]
        print(f"\n📊 CURRENT SESSION DATA: {count} records")
        
        if count > 0:
            # Get sample data with all columns
            cursor.execute(f'SELECT * FROM pre_reporting_results WHERE session_id = ? LIMIT 3', (session_id,))
            sample_rows = cursor.fetchall()
            
            print(f"\n📊 SAMPLE DATA:")
            for i, row in enumerate(sample_rows):
                print(f"   Row {i+1}:")
                for j, col_name in enumerate(column_names):
                    if j < len(row):
                        print(f"     {col_name}: {row[j]}")
                print()
            
            # Check what values are in the section-like columns
            section_columns = [col for col in column_names if 'section' in col.lower() or 'category' in col.lower()]
            
            if section_columns:
                print(f"📊 SECTION/CATEGORY COLUMNS:")
                for col in section_columns:
                    cursor.execute(f'''
                        SELECT {col}, COUNT(*) as count
                        FROM pre_reporting_results 
                        WHERE session_id = ?
                        GROUP BY {col}
                        ORDER BY count DESC
                        LIMIT 5
                    ''', (session_id,))
                    
                    values = cursor.fetchall()
                    print(f"   {col}:")
                    for val, cnt in values:
                        print(f"     {val}: {cnt}")
            else:
                print("❌ No section/category columns found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_current_pre_reporting()
