#!/usr/bin/env python3
"""
Database Query Helper with Column Mapping
Automatically handles column name inconsistencies
"""

from .database_column_mapper import get_column_mapper

class DatabaseQueryHelper:
    """Helper class for database queries with automatic column mapping"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.mapper = get_column_mapper()
    
    def get_session_data(self, session_id: str, table_name: str, limit: int = None) -> list:
        """Get all data for a session from any table with automatic column mapping"""
        try:
            session_column = self.mapper.get_session_column(table_name)
            
            query = f"SELECT * FROM {table_name} WHERE {session_column} = ?"
            params = (session_id,)
            
            if limit:
                query += f" LIMIT {limit}"
            
            rows = self.db_manager.execute_query(query, params)
            
            # Convert to standardized format
            standardized_rows = []
            for row in rows:
                if isinstance(row, dict):
                    standardized = self.mapper.standardize_row_data(table_name, row)
                    standardized_rows.append(standardized)
                else:
                    # Handle tuple/list format
                    standardized_rows.append(row)
            
            return standardized_rows
            
        except Exception as e:
            print(f"Error getting session data from {table_name}: {e}")
            return []
    
    def count_session_records(self, session_id: str, table_name: str) -> int:
        """Count records for a session in any table"""
        try:
            session_column = self.mapper.get_session_column(table_name)
            
            query = f"SELECT COUNT(*) FROM {table_name} WHERE {session_column} = ?"
            result = self.db_manager.execute_query(query, (session_id,))
            
            if result and len(result) > 0:
                return result[0][0] if isinstance(result[0], (list, tuple)) else result[0]['COUNT(*)']
            return 0
            
        except Exception as e:
            print(f"Error counting records in {table_name}: {e}")
            return 0
    
    def get_categories_breakdown(self, session_id: str, table_name: str) -> list:
        """Get category breakdown for a session with automatic column mapping"""
        try:
            session_column = self.mapper.get_session_column(table_name)
            category_column = self.mapper.get_category_column(table_name)
            
            if not category_column:
                return []
            
            query = f"""
                SELECT {category_column}, COUNT(*) as count
                FROM {table_name} 
                WHERE {session_column} = ?
                GROUP BY {category_column}
                ORDER BY count DESC
            """
            
            rows = self.db_manager.execute_query(query, (session_id,))
            
            # Standardize results
            results = []
            for row in rows:
                if isinstance(row, dict):
                    results.append({
                        'category': row.get(category_column, 'Unknown'),
                        'count': row.get('count', 0)
                    })
                elif isinstance(row, (list, tuple)):
                    results.append({
                        'category': row[0],
                        'count': row[1]
                    })
            
            return results
            
        except Exception as e:
            print(f"Error getting categories from {table_name}: {e}")
            return []
    
    def insert_standardized_data(self, table_name: str, standard_data: dict) -> bool:
        """Insert data using standard field names, automatically mapped to table columns"""
        try:
            # Convert standard data to table-specific columns
            table_data = self.mapper.prepare_insert_data(table_name, standard_data)
            
            # Build INSERT query
            columns = list(table_data.keys())
            placeholders = ', '.join(['?' for _ in columns])
            values = list(table_data.values())
            
            query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            self.db_manager.execute_update(query, values)
            return True
            
        except Exception as e:
            print(f"Error inserting data into {table_name}: {e}")
            return False
    
    def get_standardized_query(self, table_name: str, fields: list, where_conditions: dict = None) -> tuple:
        """Build a query with standardized field names"""
        try:
            # Build WHERE clause from conditions
            where_clause = ""
            params = []
            
            if where_conditions:
                conditions = []
                for field, value in where_conditions.items():
                    actual_column = self.mapper.get_column_name(table_name, field)
                    conditions.append(f"{actual_column} = ?")
                    params.append(value)
                
                where_clause = " AND ".join(conditions)
            
            # Build query with mapped columns
            query, final_params = self.mapper.build_query(table_name, fields, where_clause, tuple(params))
            
            return query, final_params
            
        except Exception as e:
            print(f"Error building standardized query: {e}")
            return "", ()
    
    def execute_standardized_query(self, table_name: str, fields: list, where_conditions: dict = None) -> list:
        """Execute a query with standardized field names and return standardized results"""
        try:
            query, params = self.get_standardized_query(table_name, fields, where_conditions)
            
            if not query:
                return []
            
            rows = self.db_manager.execute_query(query, params)
            
            # Standardize results
            standardized_rows = []
            for row in rows:
                if isinstance(row, dict):
                    standardized = self.mapper.standardize_row_data(table_name, row)
                    standardized_rows.append(standardized)
                else:
                    # Handle tuple/list format - create dict with field names
                    row_dict = {}
                    for i, field in enumerate(fields):
                        if i < len(row):
                            row_dict[field] = row[i]
                    standardized_rows.append(row_dict)
            
            return standardized_rows
            
        except Exception as e:
            print(f"Error executing standardized query: {e}")
            return []

def create_query_helper(db_manager):
    """Create a database query helper with column mapping"""
    return DatabaseQueryHelper(db_manager)
