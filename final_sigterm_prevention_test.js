#!/usr/bin/env node
/**
 * Final SIGTERM Prevention Test
 * Verify all systems are in place to prevent SIGTERM
 */

const fs = require('fs');
const path = require('path');

function finalSIGTERMPreventionTest() {
    console.log('🧪 FINAL SIGTERM PREVENTION TEST');
    console.log('=' .repeat(60));
    
    let score = 0;
    const maxScore = 10;
    
    try {
        // Test 1: UI Coordinator Integration
        console.log('\n1. 🔍 UI COORDINATOR INTEGRATION:');
        
        const indexPath = path.join(__dirname, 'index.html');
        if (fs.existsSync(indexPath)) {
            const content = fs.readFileSync(indexPath, 'utf8');
            
            if (content.includes('ui_loading_coordinator_renderer.js')) {
                console.log('   ✅ Renderer-safe UI coordinator loaded');
                score++;
            } else {
                console.log('   ❌ UI coordinator not found in index.html');
            }
        }
        
        // Test 2: Column Mapping Protection
        console.log('\n2. 🔍 COLUMN MAPPING PROTECTION:');
        
        const mainPath = path.join(__dirname, 'main.js');
        if (fs.existsSync(mainPath)) {
            const content = fs.readFileSync(mainPath, 'utf8');
            
            if (content.includes('ui_data_loader.js')) {
                console.log('   ✅ Column mapping integrated in main.js');
                score++;
            }
            
            if (content.includes('Column mapping successful')) {
                console.log('   ✅ Column mapping success logging in place');
                score++;
            }
        }
        
        // Test 3: Tracker Population Disabled
        console.log('\n3. 🔍 TRACKER POPULATION DISABLED:');
        
        if (fs.existsSync(mainPath)) {
            const content = fs.readFileSync(mainPath, 'utf8');
            
            if (content.includes('DISABLED to prevent SIGTERM')) {
                console.log('   ✅ Tracker population disabled in main.js');
                score++;
            } else {
                console.log('   ❌ Tracker population still active in main.js');
            }
        }
        
        const rendererPath = path.join(__dirname, 'renderer.js');
        if (fs.existsSync(rendererPath)) {
            const content = fs.readFileSync(rendererPath, 'utf8');
            
            if (content.includes('DISABLED to prevent SIGTERM')) {
                console.log('   ✅ Tracker population disabled in renderer.js');
                score++;
            } else {
                console.log('   ❌ Tracker population still active in renderer.js');
            }
        }
        
        // Test 4: UI Loading Conflicts Resolved
        console.log('\n4. 🔍 UI LOADING CONFLICTS RESOLVED:');
        
        if (fs.existsSync(rendererPath)) {
            const content = fs.readFileSync(rendererPath, 'utf8');
            
            const coordinatorCalls = (content.match(/getUILoadingCoordinator/g) || []).length;
            if (coordinatorCalls > 10) {
                console.log(`   ✅ ${coordinatorCalls} UI loaders using coordinator`);
                score++;
            } else {
                console.log(`   ⚠️ Only ${coordinatorCalls} UI loaders using coordinator`);
            }
            
            const oldLoaderCalls = (content.match(/loadPreReportingUIFromDatabase\(\)/g) || []).length;
            if (oldLoaderCalls === 0) {
                console.log('   ✅ No old direct UI loaders remaining');
                score++;
            } else {
                console.log(`   ❌ ${oldLoaderCalls} old direct UI loaders still present`);
            }
        }
        
        // Test 5: Database Lock Manager
        console.log('\n5. 🔍 DATABASE LOCK MANAGER:');
        
        const lockManagerPath = path.join(__dirname, 'core', 'database_lock_manager.js');
        if (fs.existsSync(lockManagerPath)) {
            const content = fs.readFileSync(lockManagerPath, 'utf8');
            
            if (content.includes('maxConcurrentConnections = 1')) {
                console.log('   ✅ Single connection limit enforced');
                score++;
            }
            
            if (content.includes('isShuttingDown')) {
                console.log('   ✅ Shutdown protection in place');
                score++;
            }
        }
        
        // Test 6: Session State
        console.log('\n6. 🔍 SESSION STATE:');
        
        try {
            const sqlite3 = require('sqlite3').verbose();
            const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
            
            const db = new sqlite3.Database(dbPath);
            
            db.get('SELECT session_id FROM current_session WHERE id = 1', (err, row) => {
                if (!err && row) {
                    console.log(`   ✅ Current session: ${row.session_id}`);
                    
                    db.get(`
                        SELECT status FROM session_phases 
                        WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
                    `, [row.session_id], (err, phaseRow) => {
                        if (!err && phaseRow) {
                            if (phaseRow.status === 'WAITING_FOR_USER') {
                                console.log('   ✅ PRE_REPORTING phase: WAITING_FOR_USER');
                                score++;
                            } else {
                                console.log(`   ⚠️ PRE_REPORTING phase: ${phaseRow.status}`);
                            }
                        }
                        
                        db.close();
                        
                        // Final scoring
                        console.log('\n🎯 FINAL SCORE:');
                        console.log(`📊 Score: ${score}/${maxScore} (${Math.round(score/maxScore*100)}%)`);
                        
                        if (score >= 8) {
                            console.log('🎉 EXCELLENT! System is ready for SIGTERM-free operation');
                            console.log('✅ All critical protections in place');
                            console.log('✅ Ready to start Electron app');
                        } else if (score >= 6) {
                            console.log('⚠️ GOOD: Most protections in place, minor issues remain');
                            console.log('💡 Should work but monitor for issues');
                        } else {
                            console.log('❌ POOR: Significant issues remain');
                            console.log('💡 Review failed tests before starting app');
                        }
                        
                        console.log('\n🚀 READY TO TEST:');
                        console.log('1. Start your Electron app');
                        console.log('2. Try loading the interactive UI');
                        console.log('3. Should work without SIGTERM errors');
                        console.log('4. Monitor console for coordinator logs');
                        
                        process.exit(score >= 6 ? 0 : 1);
                    });
                } else {
                    console.log('   ❌ No current session found');
                    db.close();
                    process.exit(1);
                }
            });
            
        } catch (error) {
            console.log(`   ⚠️ Database test error: ${error.message}`);
        }
        
    } catch (error) {
        console.error('❌ Test error:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    finalSIGTERMPreventionTest();
}
