#!/usr/bin/env node
/**
 * Trigger Final Report Generation
 * Uses the existing "Generate-FINAL REPORT button" workflow with Smart Reporter and Business Rules Engine
 * 1. Employee-Based DOCX Report for Individual High/Moderate Priority
 * 2. Excel Report for Small Bulk High Priority
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { spawn } = require('child_process');

async function triggerFinalReportGeneration() {
    console.log('🎯 TRIGGERING FINAL REPORT GENERATION');
    console.log('=' .repeat(70));
    
    try {
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        const db = new sqlite3.Database(dbPath);
        
        // Get current session
        const currentSession = await new Promise((resolve, reject) => {
            db.get('SELECT session_id FROM current_session WHERE id = 1', (err, row) => {
                if (err) reject(err);
                else resolve(row ? row.session_id : null);
            });
        });
        
        if (!currentSession) {
            console.log('❌ No current session found');
            db.close();
            return;
        }
        
        console.log(`📋 Current session: ${currentSession}`);
        
        // Get all comparison results (including NO_CHANGE for proper categorization)
        const allChanges = await new Promise((resolve, reject) => {
            db.all(`
                SELECT 
                    id,
                    employee_id,
                    employee_name,
                    section_name,
                    item_label,
                    change_type,
                    previous_value,
                    current_value,
                    numeric_difference,
                    priority
                FROM comparison_results 
                WHERE session_id = ?
                ORDER BY employee_id, section_name, item_label
            `, [currentSession], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
        
        console.log(`📊 Total comparison results: ${allChanges.length}`);
        
        // Categorize changes by bulk size and priority
        const itemGroups = {};
        
        allChanges.forEach(change => {
            const key = `${change.section_name}|${change.item_label}`;
            
            if (!itemGroups[key]) {
                itemGroups[key] = {
                    section: change.section_name,
                    item: change.item_label,
                    changes: [],
                    employees: new Set(),
                    priority: determinePriority(change.section_name)
                };
            }
            
            itemGroups[key].changes.push(change);
            itemGroups[key].employees.add(change.employee_id);
        });
        
        // Categorize changes
        const individualHighModerate = [];
        const smallBulkHigh = [];
        
        Object.values(itemGroups).forEach(group => {
            const employeeCount = group.employees.size;
            
            if (employeeCount <= 3) {
                // Individual changes
                if (group.priority === 'HIGH' || group.priority === 'MODERATE') {
                    individualHighModerate.push(...group.changes);
                }
            } else if (employeeCount >= 4 && employeeCount <= 16) {
                // Small bulk changes
                if (group.priority === 'HIGH') {
                    smallBulkHigh.push(...group.changes);
                }
            }
        });
        
        console.log(`📊 Individual High/Moderate: ${individualHighModerate.length} changes`);
        console.log(`📊 Small Bulk High: ${smallBulkHigh.length} changes`);
        
        // 1. Generate Employee-Based DOCX Report for Individual High/Moderate
        if (individualHighModerate.length > 0) {
            console.log('\n📄 GENERATING EMPLOYEE-BASED DOCX REPORT...');
            await generateEmployeeBasedReport(currentSession, individualHighModerate);
        } else {
            console.log('\n⚠️ No Individual High/Moderate priority changes found');
        }
        
        // 2. Generate Excel Report for Small Bulk High Priority
        if (smallBulkHigh.length > 0) {
            console.log('\n📊 GENERATING SMALL BULK EXCEL REPORT...');
            await generateSmallBulkExcelReport(currentSession, smallBulkHigh);
        } else {
            console.log('\n⚠️ No Small Bulk High priority changes found');
        }
        
        db.close();
        
    } catch (error) {
        console.error('❌ Error triggering final report generation:', error);
        throw error;
    }
}

function determinePriority(sectionName) {
    const normalizedSection = sectionName.toLowerCase();
    
    if (normalizedSection.includes('personal') || 
        normalizedSection.includes('earnings') || 
        normalizedSection.includes('deductions') || 
        normalizedSection.includes('bank')) {
        return 'HIGH';
    } else if (normalizedSection.includes('loan')) {
        return 'MODERATE';
    } else {
        return 'LOW';
    }
}

async function generateEmployeeBasedReport(sessionId, selectedChanges) {
    console.log('🎯 Triggering Employee-Based Report Generation...');
    
    try {
        // Update pre_reporting_results with selected changes
        await updatePreReportingResults(sessionId, selectedChanges, 'employee-based');
        
        // Call the phased_process_manager to generate final reports
        const result = await callPhasedProcessManager('generate-final-reports', sessionId);
        
        if (result.success) {
            console.log('✅ Employee-Based DOCX Report generated successfully');
            console.log(`📄 Report details:`, result);
        } else {
            console.log('❌ Employee-Based Report generation failed:', result.error);
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ Error generating Employee-Based Report:', error);
        throw error;
    }
}

async function generateSmallBulkExcelReport(sessionId, selectedChanges) {
    console.log('🎯 Triggering Small Bulk Excel Report Generation...');
    
    try {
        // Update pre_reporting_results with selected changes
        await updatePreReportingResults(sessionId, selectedChanges, 'item-based');
        
        // Call the phased_process_manager to generate final reports
        const result = await callPhasedProcessManager('generate-final-reports', sessionId);
        
        if (result.success) {
            console.log('✅ Small Bulk Excel Report generated successfully');
            console.log(`📊 Report details:`, result);
        } else {
            console.log('❌ Small Bulk Report generation failed:', result.error);
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ Error generating Small Bulk Report:', error);
        throw error;
    }
}

async function updatePreReportingResults(sessionId, selectedChanges, reportType) {
    console.log(`📝 Updating pre_reporting_results for ${reportType}...`);
    
    const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
    const db = new sqlite3.Database(dbPath);
    
    try {
        // Clear existing pre_reporting_results for this session
        await new Promise((resolve, reject) => {
            db.run('DELETE FROM pre_reporting_results WHERE session_id = ?', [sessionId], (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        
        // Insert selected changes into pre_reporting_results
        for (const change of selectedChanges) {
            await new Promise((resolve, reject) => {
                db.run(`
                    INSERT INTO pre_reporting_results (
                        session_id, employee_id, employee_name, section_name, 
                        item_label, previous_value, current_value, change_type,
                        priority, selected, report_type, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, datetime('now'))
                `, [
                    sessionId,
                    change.employee_id,
                    change.employee_name,
                    change.section_name,
                    change.item_label,
                    change.previous_value,
                    change.current_value,
                    change.change_type,
                    change.priority,
                    reportType
                ], (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
        }
        
        console.log(`✅ Updated pre_reporting_results with ${selectedChanges.length} selected changes`);
        
    } finally {
        db.close();
    }
}

async function callPhasedProcessManager(command, sessionId) {
    console.log(`🐍 Calling phased_process_manager.py ${command}...`);
    
    return new Promise((resolve, reject) => {
        const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
        const args = [command];
        if (sessionId) args.push(sessionId);
        
        const pythonProcess = spawn('python', [pythonPath, ...args], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let stdout = '';
        let stderr = '';
        
        pythonProcess.stdout.on('data', (data) => {
            stdout += data.toString();
        });
        
        pythonProcess.stderr.on('data', (data) => {
            stderr += data.toString();
        });
        
        pythonProcess.on('close', (code) => {
            if (code === 0) {
                try {
                    // Try to parse JSON result
                    const result = JSON.parse(stdout.trim());
                    resolve(result);
                } catch (error) {
                    // If not JSON, return raw output
                    resolve({ success: true, output: stdout.trim() });
                }
            } else {
                reject(new Error(`Python process failed with code ${code}: ${stderr}`));
            }
        });
        
        pythonProcess.on('error', (error) => {
            reject(error);
        });
    });
}

// Run the report generation
if (require.main === module) {
    triggerFinalReportGeneration().then(() => {
        console.log('\n🎉 FINAL REPORT GENERATION COMPLETE!');
        console.log('📄 Employee-Based DOCX Report: Individual High/Moderate Priority');
        console.log('📊 Excel Report: Small Bulk High Priority');
        console.log('🎯 Generated using Smart Reporter and Business Rules Engine');
        process.exit(0);
    }).catch(error => {
        console.error('Report generation failed:', error);
        process.exit(1);
    });
}

module.exports = { triggerFinalReportGeneration };
