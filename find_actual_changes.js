#!/usr/bin/env node
/**
 * Find Actual Changes (not NO_CHANGE)
 * Look for real payroll changes that should be in the report
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function findActualChanges() {
    console.log('🔍 FINDING ACTUAL PAYROLL CHANGES');
    console.log('=' .repeat(60));
    
    try {
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        const db = new sqlite3.Database(dbPath);
        
        // Get current session
        const currentSession = await new Promise((resolve, reject) => {
            db.get('SELECT session_id FROM current_session WHERE id = 1', (err, row) => {
                if (err) reject(err);
                else resolve(row ? row.session_id : null);
            });
        });
        
        if (!currentSession) {
            console.log('❌ No current session found');
            db.close();
            return;
        }
        
        console.log(`📋 Current session: ${currentSession}`);
        
        // Get actual changes (NOT NO_CHANGE)
        const actualChanges = await new Promise((resolve, reject) => {
            db.all(`
                SELECT 
                    employee_id,
                    employee_name,
                    section_name,
                    item_label,
                    change_type,
                    previous_value,
                    current_value,
                    numeric_difference,
                    priority
                FROM comparison_results 
                WHERE session_id = ? 
                AND change_type != 'NO_CHANGE'
                ORDER BY priority DESC, employee_id, section_name
            `, [currentSession], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
        
        console.log(`📊 Found ${actualChanges.length} actual changes (excluding NO_CHANGE)`);
        
        if (actualChanges.length === 0) {
            console.log('❌ No actual changes found - all records are NO_CHANGE');
            
            // Let's check what change types exist
            const changeTypes = await new Promise((resolve, reject) => {
                db.all(`
                    SELECT change_type, COUNT(*) as count
                    FROM comparison_results 
                    WHERE session_id = ?
                    GROUP BY change_type
                    ORDER BY count DESC
                `, [currentSession], (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                });
            });
            
            console.log('\n📊 Change Type Distribution:');
            changeTypes.forEach(type => {
                console.log(`   ${type.change_type}: ${type.count.toLocaleString()} records`);
            });
            
            db.close();
            return;
        }
        
        // Analyze the actual changes
        const changesByType = {};
        const changesByPriority = {};
        const changesByEmployee = {};
        
        actualChanges.forEach(change => {
            // By type
            if (!changesByType[change.change_type]) {
                changesByType[change.change_type] = [];
            }
            changesByType[change.change_type].push(change);
            
            // By priority
            if (!changesByPriority[change.priority]) {
                changesByPriority[change.priority] = [];
            }
            changesByPriority[change.priority].push(change);
            
            // By employee
            if (!changesByEmployee[change.employee_id]) {
                changesByEmployee[change.employee_id] = {
                    employee_name: change.employee_name,
                    changes: []
                };
            }
            changesByEmployee[change.employee_id].changes.push(change);
        });
        
        console.log('\n📈 ACTUAL CHANGES BREAKDOWN:');
        console.log('=' .repeat(60));
        
        console.log('\n🔄 By Change Type:');
        Object.entries(changesByType).forEach(([type, changes]) => {
            console.log(`   ${type}: ${changes.length} changes`);
        });
        
        console.log('\n🎯 By Priority:');
        Object.entries(changesByPriority).forEach(([priority, changes]) => {
            console.log(`   ${priority}: ${changes.length} changes`);
        });
        
        console.log('\n👥 By Employee (Top 10):');
        const topEmployees = Object.entries(changesByEmployee)
            .sort(([,a], [,b]) => b.changes.length - a.changes.length)
            .slice(0, 10);
            
        topEmployees.forEach(([empId, data]) => {
            console.log(`   ${empId}: ${data.employee_name} - ${data.changes.length} changes`);
        });
        
        // Show sample actual changes
        console.log('\n📋 SAMPLE ACTUAL CHANGES:');
        console.log('=' .repeat(60));
        
        actualChanges.slice(0, 10).forEach((change, i) => {
            console.log(`${i + 1}. ${change.employee_id}: ${change.employee_name}`);
            console.log(`   Section: ${change.section_name}`);
            console.log(`   Item: ${change.item_label}`);
            console.log(`   Change: ${change.change_type}`);
            console.log(`   Previous: ${change.previous_value}`);
            console.log(`   Current: ${change.current_value}`);
            console.log(`   Difference: ${change.numeric_difference}`);
            console.log(`   Priority: ${change.priority}`);
            console.log('');
        });
        
        // Check for individual vs bulk changes
        console.log('\n🔍 INDIVIDUAL VS BULK ANALYSIS:');
        console.log('=' .repeat(60));
        
        const itemGroups = {};
        actualChanges.forEach(change => {
            const key = `${change.section_name}|${change.item_label}`;
            if (!itemGroups[key]) {
                itemGroups[key] = {
                    section: change.section_name,
                    item: change.item_label,
                    employees: new Set(),
                    changes: []
                };
            }
            itemGroups[key].employees.add(change.employee_id);
            itemGroups[key].changes.push(change);
        });
        
        let individualCount = 0;
        let smallBulkCount = 0;
        let mediumBulkCount = 0;
        let largeBulkCount = 0;
        
        Object.values(itemGroups).forEach(group => {
            const empCount = group.employees.size;
            if (empCount <= 3) {
                individualCount += group.changes.length;
            } else if (empCount <= 16) {
                smallBulkCount += group.changes.length;
            } else if (empCount <= 32) {
                mediumBulkCount += group.changes.length;
            } else {
                largeBulkCount += group.changes.length;
            }
        });
        
        console.log(`Individual (≤3 employees): ${individualCount} changes`);
        console.log(`Small Bulk (4-16 employees): ${smallBulkCount} changes`);
        console.log(`Medium Bulk (17-32 employees): ${mediumBulkCount} changes`);
        console.log(`Large Bulk (33+ employees): ${largeBulkCount} changes`);
        
        db.close();
        
        return {
            totalChanges: actualChanges.length,
            changesByType,
            changesByPriority,
            changesByEmployee,
            itemGroups,
            categorization: {
                individual: individualCount,
                smallBulk: smallBulkCount,
                mediumBulk: mediumBulkCount,
                largeBulk: largeBulkCount
            }
        };
        
    } catch (error) {
        console.error('❌ Error finding actual changes:', error);
        throw error;
    }
}

// Run the analysis
if (require.main === module) {
    findActualChanges().then(result => {
        if (result && result.totalChanges > 0) {
            console.log('\n🎉 ACTUAL CHANGES FOUND!');
            console.log(`Total: ${result.totalChanges} real changes`);
            console.log('Ready to generate proper reports with actual data!');
        } else {
            console.log('\n⚠️ NO ACTUAL CHANGES FOUND');
            console.log('All records appear to be NO_CHANGE - need to investigate data source');
        }
        process.exit(0);
    }).catch(error => {
        console.error('Analysis failed:', error);
        process.exit(1);
    });
}

module.exports = { findActualChanges };
