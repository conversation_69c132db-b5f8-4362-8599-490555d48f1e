// Clear any test data that might be showing in the UI
console.log('🧹 CLEARING TEST DATA FROM UI');

// Clear file info displays
const currentInfo = document.getElementById('current-payroll-info');
const previousInfo = document.getElementById('previous-payroll-info');

if (currentInfo) {
    currentInfo.style.display = 'none';
    currentInfo.innerHTML = '';
    console.log('✅ Cleared current file info display');
}

if (previousInfo) {
    previousInfo.style.display = 'none';
    previousInfo.innerHTML = '';
    console.log('✅ Cleared previous file info display');
}

// Reset variables
window.auditCurrentPdfPath = null;
window.auditPreviousPdfPath = null;

// Reset button state
const startBtn = document.getElementById('start-payroll-audit');
if (startBtn) {
    startBtn.disabled = true;
    startBtn.style.opacity = '0.6';
    startBtn.style.cursor = 'not-allowed';
    startBtn.style.backgroundColor = '#6c757d';
    startBtn.style.boxShadow = 'none';
    console.log('✅ Reset start button to disabled state');
}

console.log('✅ Test data cleared - UI should now be clean');
