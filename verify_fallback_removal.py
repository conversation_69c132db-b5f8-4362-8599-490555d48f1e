#!/usr/bin/env python3
"""Verify that the toxic fallback has been removed"""

def verify_fallback_removal():
    """Check that the fallback code has been completely removed"""
    print("🔍 VERIFYING FALLBACK REMOVAL")
    print("=" * 50)
    
    try:
        with open('core/phased_process_manager.py', 'r') as f:
            content = f.read()
        
        # Check for fallback indicators
        fallback_indicators = [
            'Fallback to old method',
            'execute_update',
            'INSERT INTO audit_sessions',
            'UNIQUE constraint failed',
            'retry_count',
            'max_retries'
        ]
        
        print("🔍 CHECKING FOR FALLBACK CODE REMNANTS:")
        
        found_fallback = False
        for indicator in fallback_indicators:
            if indicator in content:
                print(f"   ❌ Found: '{indicator}'")
                found_fallback = True
            else:
                print(f"   ✅ Removed: '{indicator}'")
        
        if not found_fallback:
            print("\n🎉 SUCCESS: All fallback code has been removed!")
            print("✅ System will now ONLY use unified session manager")
            print("✅ No more incomplete session creation")
            print("✅ No more database inconsistencies")
        else:
            print("\n⚠️ WARNING: Some fallback code may still exist")
        
        # Check what the create_session method looks like now
        print("\n📋 CURRENT CREATE_SESSION METHOD:")
        lines = content.split('\n')
        in_create_session = False
        method_lines = []
        
        for line in lines:
            if 'def create_session(' in line:
                in_create_session = True
                method_lines.append(line)
            elif in_create_session:
                if line.strip().startswith('def ') and 'create_session' not in line:
                    break
                method_lines.append(line)
        
        if method_lines:
            print("   Current implementation:")
            for i, line in enumerate(method_lines[:20]):  # Show first 20 lines
                print(f"   {i+1:2d}: {line}")
            if len(method_lines) > 20:
                print(f"   ... ({len(method_lines) - 20} more lines)")
        
        print("\n📋 EXPECTED BEHAVIOR:")
        print("✅ Session creation will ONLY use unified_session_manager")
        print("✅ If unified manager fails, the process will fail cleanly")
        print("✅ No more partial sessions that break the UI")
        print("✅ All sessions will have proper audit_sessions records")
        print("✅ All sessions will have proper current_session pointers")
        
        return not found_fallback
        
    except Exception as e:
        print(f"❌ Error checking file: {e}")
        return False

if __name__ == "__main__":
    success = verify_fallback_removal()
    
    if success:
        print("\n🎯 NEXT STEPS:")
        print("1. The toxic fallback has been removed")
        print("2. System will now fail cleanly if session creation fails")
        print("3. This forces proper debugging of unified session manager")
        print("4. No more mysterious broken sessions")
    else:
        print("\n⚠️ Manual verification needed - some fallback code may remain")
