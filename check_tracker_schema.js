#!/usr/bin/env node
/**
 * Check tracker_results table schema
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

function checkTrackerSchema() {
    console.log('🔍 CHECKING TRACKER_RESULTS SCHEMA');
    console.log('=' .repeat(40));
    
    try {
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        const db = new sqlite3.Database(dbPath);
        
        // Get table schema
        db.all("PRAGMA table_info(tracker_results)", (err, columns) => {
            if (err) {
                console.error('Error getting schema:', err);
                db.close();
                return;
            }
            
            console.log('📋 TRACKER_RESULTS COLUMNS:');
            columns.forEach(col => {
                console.log(`   ${col.name} (${col.type})`);
            });
            
            // Get sample data
            db.all("SELECT * FROM tracker_results LIMIT 3", (err, rows) => {
                if (err) {
                    console.error('Error getting sample data:', err);
                } else if (rows.length > 0) {
                    console.log('\n📊 SAMPLE DATA:');
                    rows.forEach((row, i) => {
                        console.log(`   Row ${i + 1}:`);
                        Object.keys(row).forEach(key => {
                            console.log(`     ${key}: ${row[key]}`);
                        });
                        console.log();
                    });
                } else {
                    console.log('\n📊 No sample data found');
                }
                
                db.close();
            });
        });
        
    } catch (error) {
        console.error('Error:', error);
    }
}

checkTrackerSchema();
