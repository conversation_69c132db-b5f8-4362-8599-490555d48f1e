// Diagnostic script to test file display issues
console.log('🔍 DIAGNOSING FILE DISPLAY ISSUES');

// Test function to simulate file selection
function testFileSelection(type, testFilePath = 'C:\\Test\\sample-payroll.pdf') {
    console.log(`\n🧪 Testing ${type} file selection...`);
    
    // Step 1: Check if elements exist
    const elementId = type === 'current' ? 'current-payroll-info' : 'previous-payroll-info';
    const infoElement = document.getElementById(elementId);
    
    console.log(`1. Element check for '${elementId}':`, infoElement ? '✅ Found' : '❌ Not found');
    
    if (!infoElement) {
        console.error(`❌ Element '${elementId}' not found in DOM`);
        return false;
    }
    
    // Step 2: Check initial styles
    const computedStyle = window.getComputedStyle(infoElement);
    console.log(`2. Initial display style:`, computedStyle.display);
    console.log(`2. Initial visibility:`, computedStyle.visibility);
    console.log(`2. Initial opacity:`, computedStyle.opacity);
    
    // Step 3: Test updateAuditFileInfo function
    console.log(`3. Testing updateAuditFileInfo...`);
    
    try {
        // Simulate the function
        const fileName = testFilePath.split('\\').pop().split('/').pop();
        infoElement.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${fileName}`;
        infoElement.style.display = 'block';
        
        console.log(`3. File name extracted: ${fileName}`);
        console.log(`3. HTML updated: ${infoElement.innerHTML}`);
        console.log(`3. Display set to: ${infoElement.style.display}`);
        
        // Check final computed style
        const finalStyle = window.getComputedStyle(infoElement);
        console.log(`3. Final computed display:`, finalStyle.display);
        console.log(`3. Final computed visibility:`, finalStyle.visibility);
        
        return true;
    } catch (error) {
        console.error(`❌ Error in updateAuditFileInfo:`, error);
        return false;
    }
}

// Test button state function
function testButtonState() {
    console.log(`\n🧪 Testing button state...`);
    
    const startButton = document.getElementById('start-payroll-audit');
    console.log(`1. Start button found:`, startButton ? '✅ Yes' : '❌ No');
    
    if (!startButton) {
        console.error(`❌ Start button not found`);
        return false;
    }
    
    // Check current state
    console.log(`2. Button disabled:`, startButton.disabled);
    console.log(`2. Button style opacity:`, startButton.style.opacity);
    console.log(`2. Button style cursor:`, startButton.style.cursor);
    console.log(`2. Button style backgroundColor:`, startButton.style.backgroundColor);
    
    // Check file paths
    console.log(`3. auditCurrentPdfPath:`, window.auditCurrentPdfPath || 'null');
    console.log(`3. auditPreviousPdfPath:`, window.auditPreviousPdfPath || 'null');
    
    // Test button enable logic
    const hasCurrentPdf = !!window.auditCurrentPdfPath;
    const hasPreviousPdf = !!window.auditPreviousPdfPath;
    const allRequirementsMet = hasCurrentPdf && hasPreviousPdf;
    
    console.log(`4. Has current PDF:`, hasCurrentPdf);
    console.log(`4. Has previous PDF:`, hasPreviousPdf);
    console.log(`4. All requirements met:`, allRequirementsMet);
    
    return allRequirementsMet;
}

// Test CSS conflicts
function testCSSConflicts() {
    console.log(`\n🧪 Testing CSS conflicts...`);
    
    const elements = ['current-payroll-info', 'previous-payroll-info'];
    
    elements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            const style = window.getComputedStyle(element);
            console.log(`${elementId}:`);
            console.log(`  display: ${style.display}`);
            console.log(`  visibility: ${style.visibility}`);
            console.log(`  opacity: ${style.opacity}`);
            console.log(`  height: ${style.height}`);
            console.log(`  overflow: ${style.overflow}`);
            console.log(`  position: ${style.position}`);
        }
    });
}

// Main diagnostic function
function runDiagnostics() {
    console.log('🔍 RUNNING COMPLETE FILE DISPLAY DIAGNOSTICS');
    console.log('='.repeat(50));
    
    // Test 1: CSS conflicts
    testCSSConflicts();
    
    // Test 2: File selection simulation
    const currentResult = testFileSelection('current');
    const previousResult = testFileSelection('previous');
    
    // Test 3: Button state
    const buttonResult = testButtonState();
    
    // Summary
    console.log(`\n📋 DIAGNOSTIC SUMMARY:`);
    console.log(`Current file display test: ${currentResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Previous file display test: ${previousResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Button state test: ${buttonResult ? '✅ PASS' : '❌ FAIL'}`);
    
    if (currentResult && previousResult && buttonResult) {
        console.log(`\n🎉 ALL TESTS PASSED - File display should work!`);
    } else {
        console.log(`\n❌ SOME TESTS FAILED - Issues need to be fixed`);
    }
}

// Export functions for manual testing
window.testFileSelection = testFileSelection;
window.testButtonState = testButtonState;
window.testCSSConflicts = testCSSConflicts;
window.runDiagnostics = runDiagnostics;

console.log('✅ Diagnostic functions loaded. Run runDiagnostics() to test everything.');
