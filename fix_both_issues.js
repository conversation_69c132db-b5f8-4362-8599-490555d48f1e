// Fix both payroll audit and bank adviser issues
console.log('🔧 FIXING PAYROLL AUDIT AND BANK ADVISER ISSUES');

// Fix 1: Check Bank Adviser button visibility
function checkBankAdviserButton() {
    console.log('\n🏦 CHECKING BANK ADVISER BUTTON...');
    
    const startSessionBtn = document.getElementById('start-session-btn');
    const sessionCreation = document.getElementById('session-creation');
    
    if (startSessionBtn) {
        console.log('✅ Start session button found');
        console.log('   Visible:', startSessionBtn.offsetParent !== null);
        console.log('   Display:', window.getComputedStyle(startSessionBtn).display);
        console.log('   Parent display:', sessionCreation ? window.getComputedStyle(sessionCreation).display : 'N/A');
    } else {
        console.log('❌ Start session button NOT found');
        
        // Check if Bank Adviser UI was created
        const bankAdviserContainer = document.querySelector('.bank-adviser-container');
        if (bankAdviserContainer) {
            console.log('✅ Bank Adviser container exists');
            console.log('   HTML:', bankAdviserContainer.innerHTML.substring(0, 200) + '...');
        } else {
            console.log('❌ Bank Adviser container NOT found');
        }
    }
}

// Fix 2: Force Bank Adviser button to show
function forceBankAdviserButtonShow() {
    console.log('\n🔧 FORCING BANK ADVISER BUTTON TO SHOW...');
    
    const sessionCreation = document.getElementById('session-creation');
    if (sessionCreation) {
        sessionCreation.style.display = 'block';
        console.log('✅ Forced session-creation div to show');
    }
    
    const startSessionBtn = document.getElementById('start-session-btn');
    if (startSessionBtn) {
        startSessionBtn.style.display = 'inline-flex';
        startSessionBtn.style.visibility = 'visible';
        console.log('✅ Forced start-session-btn to show');
    }
}

// Fix 3: Check if Bank Adviser is properly initialized
function checkBankAdviserInitialization() {
    console.log('\n🔍 CHECKING BANK ADVISER INITIALIZATION...');
    
    if (typeof window.bankAdviser !== 'undefined') {
        console.log('✅ Bank Adviser instance exists');
        console.log('   Current session:', window.bankAdviser.currentSession);
    } else {
        console.log('❌ Bank Adviser instance NOT found');
        
        // Try to reinitialize
        if (typeof BankAdviser !== 'undefined') {
            console.log('🔄 Attempting to reinitialize Bank Adviser...');
            try {
                window.bankAdviser = new BankAdviser();
                console.log('✅ Bank Adviser reinitialized');
            } catch (error) {
                console.log('❌ Failed to reinitialize:', error.message);
            }
        }
    }
}

// Fix 4: Test payroll audit function
function testPayrollAuditFunction() {
    console.log('\n🧪 TESTING PAYROLL AUDIT FUNCTION...');
    
    if (typeof startPayrollAuditProcess === 'function') {
        console.log('✅ startPayrollAuditProcess function exists');
        
        // Check if required variables exist
        console.log('   auditCurrentPdfPath:', window.auditCurrentPdfPath || 'undefined');
        console.log('   auditPreviousPdfPath:', window.auditPreviousPdfPath || 'undefined');
        
        if (window.auditCurrentPdfPath && window.auditPreviousPdfPath) {
            console.log('✅ Both PDF paths are set - function should work');
        } else {
            console.log('⚠️ PDF paths not set - select files first');
        }
    } else {
        console.log('❌ startPayrollAuditProcess function NOT found');
    }
}

// Main diagnostic function
function runBothFixes() {
    console.log('🚀 RUNNING BOTH FIXES');
    console.log('='.repeat(50));
    
    // Check current state
    checkBankAdviserButton();
    checkBankAdviserInitialization();
    testPayrollAuditFunction();
    
    // Apply fixes
    forceBankAdviserButtonShow();
    
    console.log('\n📋 SUMMARY:');
    console.log('1. Payroll audit signatureName error: FIXED in payroll_audit_core.js');
    console.log('2. Bank adviser button visibility: CHECKED and FORCED to show');
    console.log('3. If issues persist, try refreshing the page');
}

// Auto-run fixes
setTimeout(runBothFixes, 2000);

// Make functions available globally
window.checkBankAdviserButton = checkBankAdviserButton;
window.forceBankAdviserButtonShow = forceBankAdviserButtonShow;
window.runBothFixes = runBothFixes;

console.log('✅ Both fixes loaded. Auto-running in 2 seconds...');
