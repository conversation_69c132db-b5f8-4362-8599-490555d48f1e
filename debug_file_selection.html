<!DOCTYPE html>
<html>
<head>
    <title>Debug File Selection</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .btn { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; font-family: monospace; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        .warning { background: #fff3cd; }
    </style>
</head>
<body>
    <h1>🔍 File Selection Debug Tool</h1>
    
    <div class="debug-section">
        <h3>1. Test File Selection API</h3>
        <button id="test-api" class="btn">Test window.api.selectPdfFile()</button>
        <div id="api-result" class="result">Click button to test API</div>
    </div>
    
    <div class="debug-section">
        <h3>2. Test Element IDs</h3>
        <button id="test-elements" class="btn">Check Element IDs</button>
        <div id="element-result" class="result">Click button to check elements</div>
    </div>
    
    <div class="debug-section">
        <h3>3. Test File Info Update</h3>
        <button id="test-file-info" class="btn">Test updateAuditFileInfo()</button>
        <div id="file-info-result" class="result">Click button to test file info update</div>
        
        <!-- Test elements -->
        <div id="current-payroll-info" class="file-info" style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
            Current file info will appear here
        </div>
        <div id="previous-payroll-info" class="file-info" style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
            Previous file info will appear here
        </div>
    </div>
    
    <div class="debug-section">
        <h3>4. Test Complete Flow</h3>
        <button id="test-current-flow" class="btn">Test Current File Flow</button>
        <button id="test-previous-flow" class="btn">Test Previous File Flow</button>
        <div id="flow-result" class="result">Click buttons to test complete flow</div>
    </div>

    <script>
        // Mock variables
        let auditCurrentPdfPath = null;
        let auditPreviousPdfPath = null;
        
        // Test 1: API Test
        document.getElementById('test-api').addEventListener('click', async () => {
            const result = document.getElementById('api-result');
            
            try {
                result.innerHTML = '🔄 Testing API...';
                result.className = 'result warning';
                
                if (typeof window.api?.selectPdfFile === 'function') {
                    const filePath = await window.api.selectPdfFile();
                    
                    if (filePath) {
                        result.innerHTML = `✅ API works! Selected: ${filePath}`;
                        result.className = 'result success';
                    } else {
                        result.innerHTML = 'ℹ️ API works but no file selected (user canceled)';
                        result.className = 'result warning';
                    }
                } else {
                    result.innerHTML = '❌ window.api.selectPdfFile is not available';
                    result.className = 'result error';
                }
            } catch (error) {
                result.innerHTML = `❌ API Error: ${error.message}`;
                result.className = 'result error';
            }
        });
        
        // Test 2: Element Check
        document.getElementById('test-elements').addEventListener('click', () => {
            const result = document.getElementById('element-result');
            
            const elements = [
                'current-payroll-info',
                'previous-payroll-info',
                'start-payroll-audit',
                'browse-current-payroll',
                'browse-previous-payroll'
            ];
            
            let output = 'Element Check Results:\\n';
            let allFound = true;
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    output += `✅ ${id}: Found\\n`;
                } else {
                    output += `❌ ${id}: NOT FOUND\\n`;
                    allFound = false;
                }
            });
            
            result.innerHTML = output;
            result.className = allFound ? 'result success' : 'result error';
        });
        
        // Test 3: File Info Update
        document.getElementById('test-file-info').addEventListener('click', () => {
            const result = document.getElementById('file-info-result');
            
            // Test updateAuditFileInfo function
            function testUpdateAuditFileInfo(elementId, filePath) {
                const infoElement = document.getElementById(elementId);
                if (infoElement) {
                    const fileName = filePath.split('\\\\').pop().split('/').pop();
                    infoElement.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${fileName}`;
                    infoElement.style.display = 'block';
                    return true;
                } else {
                    return false;
                }
            }
            
            const testPath = 'C:\\\\Test\\\\Folder\\\\test-payroll.pdf';
            
            const currentResult = testUpdateAuditFileInfo('current-payroll-info', testPath);
            const previousResult = testUpdateAuditFileInfo('previous-payroll-info', testPath);
            
            if (currentResult && previousResult) {
                result.innerHTML = '✅ updateAuditFileInfo function works correctly';
                result.className = 'result success';
            } else {
                result.innerHTML = `❌ updateAuditFileInfo failed. Current: ${currentResult}, Previous: ${previousResult}`;
                result.className = 'result error';
            }
        });
        
        // Test 4: Complete Flow
        async function testCompleteFlow(type) {
            const result = document.getElementById('flow-result');
            
            try {
                result.innerHTML = `🔄 Testing ${type} file selection flow...`;
                result.className = 'result warning';
                
                // Step 1: API call
                if (typeof window.api?.selectPdfFile !== 'function') {
                    throw new Error('API not available');
                }
                
                const filePath = await window.api.selectPdfFile();
                
                if (!filePath) {
                    result.innerHTML = `ℹ️ ${type} flow test canceled (no file selected)`;
                    result.className = 'result warning';
                    return;
                }
                
                // Step 2: Update variables
                if (type === 'current') {
                    auditCurrentPdfPath = filePath;
                    window.auditCurrentPdfPath = filePath;
                } else {
                    auditPreviousPdfPath = filePath;
                    window.auditPreviousPdfPath = filePath;
                }
                
                // Step 3: Update UI
                const elementId = type === 'current' ? 'current-payroll-info' : 'previous-payroll-info';
                const infoElement = document.getElementById(elementId);
                
                if (infoElement) {
                    const fileName = filePath.split('\\\\').pop().split('/').pop();
                    infoElement.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${fileName}`;
                    infoElement.style.display = 'block';
                }
                
                result.innerHTML = `✅ ${type} file flow completed successfully!\\nFile: ${filePath}`;
                result.className = 'result success';
                
            } catch (error) {
                result.innerHTML = `❌ ${type} flow failed: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        document.getElementById('test-current-flow').addEventListener('click', () => testCompleteFlow('current'));
        document.getElementById('test-previous-flow').addEventListener('click', () => testCompleteFlow('previous'));
    </script>
</body>
</html>
