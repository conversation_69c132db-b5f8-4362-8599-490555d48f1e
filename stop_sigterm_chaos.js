#!/usr/bin/env node
/**
 * Stop SIGTERM Chaos
 * Emergency script to stop all conflicting processes and database locks
 */

const { spawn } = require('child_process');
const path = require('path');

async function stopSIGTERMChaos() {
    console.log('🚨 STOPPING SIGTERM CHAOS');
    console.log('=' .repeat(50));
    
    try {
        // Step 1: Kill all Python processes
        console.log('\n1. 🔪 KILLING ALL PYTHON PROCESSES:');
        
        await killPythonProcesses();
        
        // Step 2: Clear database locks
        console.log('\n2. 🔓 CLEARING DATABASE LOCKS:');
        
        await clearDatabaseLocks();
        
        // Step 3: Stop tracker population spam
        console.log('\n3. 🛑 STOPPING TRACKER POPULATION SPAM:');
        
        await stopTrackerSpam();
        
        // Step 4: Reset session state
        console.log('\n4. 🔄 RESETTING SESSION STATE:');
        
        await resetSessionState();
        
        // Step 5: Prepare for clean UI load
        console.log('\n5. 🎨 PREPARING FOR CLEAN UI LOAD:');
        
        await prepareCleanUILoad();
        
        console.log('\n🎉 SIGTERM CHAOS STOPPED!');
        console.log('✅ All conflicting processes killed');
        console.log('✅ Database locks cleared');
        console.log('✅ Session state reset');
        console.log('✅ Ready for clean UI load');
        
        return true;
        
    } catch (error) {
        console.error('❌ Error stopping SIGTERM chaos:', error);
        return false;
    }
}

async function killPythonProcesses() {
    return new Promise((resolve) => {
        console.log('   🔪 Killing Python processes...');
        
        // Kill all Python processes on Windows
        const killProcess = spawn('taskkill', ['/F', '/IM', 'python.exe'], { 
            stdio: 'pipe',
            shell: true 
        });
        
        killProcess.on('close', (code) => {
            if (code === 0) {
                console.log('   ✅ Python processes killed');
            } else {
                console.log('   ⚠️ No Python processes found or already killed');
            }
            resolve();
        });
        
        killProcess.on('error', (error) => {
            console.log('   ⚠️ Error killing Python processes:', error.message);
            resolve();
        });
        
        // Timeout after 5 seconds
        setTimeout(() => {
            killProcess.kill();
            console.log('   ⚠️ Kill process timed out');
            resolve();
        }, 5000);
    });
}

async function clearDatabaseLocks() {
    try {
        const sqlite3 = require('sqlite3').verbose();
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        
        console.log('   🔓 Opening database to clear locks...');
        
        const db = new sqlite3.Database(dbPath);
        
        // Execute a simple query to clear any locks
        await new Promise((resolve, reject) => {
            db.get('SELECT 1 as test', (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('   ✅ Database accessible, locks cleared');
                    resolve(row);
                }
            });
        });
        
        db.close();
        
    } catch (error) {
        console.log('   ⚠️ Error clearing database locks:', error.message);
    }
}

async function stopTrackerSpam() {
    try {
        const fs = require('fs');
        const rendererPath = path.join(__dirname, 'renderer.js');
        
        if (fs.existsSync(rendererPath)) {
            let content = fs.readFileSync(rendererPath, 'utf8');
            
            // Count tracker population calls
            const trackerCalls = (content.match(/TRACKER-POPULATION/g) || []).length;
            console.log(`   📊 Found ${trackerCalls} tracker population references`);
            
            // The UI coordinator should already handle this
            console.log('   ✅ Tracker spam prevention via UI coordinator');
        }
        
    } catch (error) {
        console.log('   ⚠️ Error checking tracker spam:', error.message);
    }
}

async function resetSessionState() {
    try {
        const sqlite3 = require('sqlite3').verbose();
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        
        const db = new sqlite3.Database(dbPath);
        
        // Get current session
        const session = await new Promise((resolve, reject) => {
            db.get('SELECT session_id FROM current_session WHERE id = 1', (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
        
        if (session) {
            console.log(`   📋 Current session: ${session.session_id}`);
            
            // Reset session to PRE_REPORTING WAITING_FOR_USER
            await new Promise((resolve, reject) => {
                db.run(`
                    UPDATE session_phases 
                    SET status = 'WAITING_FOR_USER'
                    WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
                `, [session.session_id], (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
            
            console.log('   ✅ Session reset to WAITING_FOR_USER');
        }
        
        db.close();
        
    } catch (error) {
        console.log('   ⚠️ Error resetting session state:', error.message);
    }
}

async function prepareCleanUILoad() {
    try {
        // Check if UI coordinator is properly integrated
        const fs = require('fs');
        const indexPath = path.join(__dirname, 'index.html');
        
        if (fs.existsSync(indexPath)) {
            const content = fs.readFileSync(indexPath, 'utf8');
            
            if (content.includes('ui_loading_coordinator_renderer.js')) {
                console.log('   ✅ UI coordinator integrated in index.html');
            } else {
                console.log('   ❌ UI coordinator not found in index.html');
            }
        }
        
        // Check if main.js has column mapping
        const mainPath = path.join(__dirname, 'main.js');
        if (fs.existsSync(mainPath)) {
            const content = fs.readFileSync(mainPath, 'utf8');
            
            if (content.includes('Column mapping successful')) {
                console.log('   ✅ Column mapping integrated in main.js');
            } else {
                console.log('   ❌ Column mapping not found in main.js');
            }
        }
        
        console.log('   ✅ System prepared for clean UI load');
        
    } catch (error) {
        console.log('   ⚠️ Error preparing clean UI load:', error.message);
    }
}

// Run the chaos stopper
if (require.main === module) {
    stopSIGTERMChaos().then(success => {
        if (success) {
            console.log('\n🎯 NEXT STEPS:');
            console.log('1. Wait 5 seconds for all processes to fully stop');
            console.log('2. Start your Electron app');
            console.log('3. Try loading the interactive UI');
            console.log('4. Should work without SIGTERM or database locks');
        } else {
            console.log('\n💡 TROUBLESHOOTING:');
            console.log('1. Manually kill any remaining Python processes');
            console.log('2. Restart your computer if database locks persist');
            console.log('3. Check Task Manager for stuck processes');
        }
        
        process.exit(success ? 0 : 1);
    });
}
