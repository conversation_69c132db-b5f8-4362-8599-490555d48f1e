#!/usr/bin/env python3
"""
Database Column Mapping Layer
Handles all column name inconsistencies automatically
"""

class DatabaseColumnMapper:
    """Maps inconsistent column names to standardized field names"""
    
    def __init__(self):
        # Define column mappings for each table
        self.table_mappings = {
            'comparison_results': {
                'session_ref': 'session_id',
                'employee_ref': 'employee_id', 
                'section': 'section_name',
                'category': 'section_name',  # section_name is the source
                'item': 'item_label',
                'value': 'current_value'
            },
            'pre_reporting_results': {
                'session_ref': 'session_id',
                'employee_ref': 'change_id',  # no direct employee column, use change_id
                'section': 'bulk_category',  # bulk_category is the grouping column
                'category': 'bulk_category',  # bulk_category is the actual column
                'item': 'change_id',  # change_id is the item reference
                'value': 'bulk_size'  # bulk_size is the numeric value
            },
            'extracted_data': {
                'session_ref': 'session_id',
                'employee_ref': 'employee_id',
                'section': 'section_name',
                'category': 'section_name',
                'item': 'item_label',
                'value': 'item_value'
            },
            'tracker_results': {
                'session_ref': 'session_id',
                'employee_ref': 'employee_id',
                'section': 'tracker_type',
                'category': 'tracker_type',
                'item': 'item_label',
                'value': 'item_value'
            },
            'in_house_loans': {
                'session_ref': 'source_session',
                'employee_ref': 'employee_no',
                'section': 'loan_type',
                'category': 'loan_type',
                'item': 'loan_type',
                'value': 'loan_amount'
            },
            'external_loans': {
                'session_ref': 'source_session', 
                'employee_ref': 'employee_no',
                'section': 'loan_type',
                'category': 'loan_type',
                'item': 'loan_type',
                'value': 'loan_amount'
            },
            'motor_vehicle_maintenance': {
                'session_ref': 'source_session',
                'employee_ref': 'employee_no', 
                'section': 'allowance_type',
                'category': 'allowance_type',
                'item': 'allowance_type',
                'value': 'payable_amount'
            },
            'audit_sessions': {
                'session_ref': 'session_id',
                'employee_ref': None,
                'section': None,
                'category': None,
                'item': None,
                'value': None
            },
            'session_phases': {
                'session_ref': 'session_id',
                'employee_ref': None,
                'section': 'phase_name',
                'category': 'phase_name', 
                'item': 'phase_name',
                'value': 'data_count'
            }
        }
        
        # Reverse mappings for data conversion
        self.field_mappings = {
            'session_ref': ['session_id', 'source_session'],
            'employee_ref': ['employee_id', 'employee_no'],
            'section': ['section_name', 'category', 'tracker_type', 'loan_type', 'allowance_type', 'phase_name'],
            'category': ['category', 'section_name', 'tracker_type', 'loan_type', 'allowance_type', 'phase_name'],
            'item': ['item_label', 'loan_type', 'allowance_type', 'phase_name'],
            'value': ['current_value', 'item_value', 'loan_amount', 'payable_amount', 'data_count']
        }
    
    def get_column_name(self, table_name: str, standard_field: str) -> str:
        """Get the actual column name for a standard field in a specific table"""
        if table_name not in self.table_mappings:
            # Unknown table, return standard field as-is
            return standard_field
            
        mapping = self.table_mappings[table_name]
        return mapping.get(standard_field, standard_field)
    
    def build_query(self, table_name: str, standard_fields: list, where_clause: str = "", params: tuple = ()) -> tuple:
        """Build a query with correct column names for the table"""
        if table_name not in self.table_mappings:
            # Unknown table, use fields as-is
            fields_str = ', '.join(standard_fields)
            query = f"SELECT {fields_str} FROM {table_name}"
            if where_clause:
                query += f" WHERE {where_clause}"
            return query, params
        
        # Map standard fields to actual column names
        actual_columns = []
        for field in standard_fields:
            actual_column = self.get_column_name(table_name, field)
            if actual_column:
                actual_columns.append(actual_column)
        
        fields_str = ', '.join(actual_columns)
        query = f"SELECT {fields_str} FROM {table_name}"
        
        if where_clause:
            # Map standard field names in WHERE clause
            mapped_where = self._map_where_clause(table_name, where_clause)
            query += f" WHERE {mapped_where}"
        
        return query, params
    
    def _map_where_clause(self, table_name: str, where_clause: str) -> str:
        """Map standard field names in WHERE clause to actual column names"""
        mapped_clause = where_clause
        
        if table_name in self.table_mappings:
            mapping = self.table_mappings[table_name]
            
            # Replace standard field names with actual column names
            for standard_field, actual_column in mapping.items():
                if actual_column and standard_field in mapped_clause:
                    # Use word boundaries to avoid partial replacements
                    import re
                    pattern = r'\b' + re.escape(standard_field) + r'\b'
                    mapped_clause = re.sub(pattern, actual_column, mapped_clause)
        
        return mapped_clause
    
    def standardize_row_data(self, table_name: str, row_data: dict) -> dict:
        """Convert row data from table-specific columns to standard field names"""
        if table_name not in self.table_mappings:
            return row_data
        
        mapping = self.table_mappings[table_name]
        standardized = {}
        
        # Create reverse mapping
        reverse_mapping = {v: k for k, v in mapping.items() if v}
        
        for column_name, value in row_data.items():
            # Find standard field name for this column
            standard_field = reverse_mapping.get(column_name, column_name)
            standardized[standard_field] = value
        
        return standardized
    
    def prepare_insert_data(self, table_name: str, standard_data: dict) -> dict:
        """Convert standard field data to table-specific column names for INSERT"""
        if table_name not in self.table_mappings:
            return standard_data
        
        mapping = self.table_mappings[table_name]
        table_data = {}
        
        for standard_field, value in standard_data.items():
            actual_column = mapping.get(standard_field)
            if actual_column:
                table_data[actual_column] = value
            else:
                # Field not mapped, use as-is
                table_data[standard_field] = value
        
        return table_data
    
    def get_session_column(self, table_name: str) -> str:
        """Get the session reference column name for a table"""
        return self.get_column_name(table_name, 'session_ref')
    
    def get_employee_column(self, table_name: str) -> str:
        """Get the employee reference column name for a table"""
        return self.get_column_name(table_name, 'employee_ref')
    
    def get_category_column(self, table_name: str) -> str:
        """Get the category/section column name for a table"""
        return self.get_column_name(table_name, 'category')

# Global instance
column_mapper = DatabaseColumnMapper()

def get_column_mapper():
    """Get the global column mapper instance"""
    return column_mapper
