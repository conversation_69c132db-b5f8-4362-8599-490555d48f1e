#!/usr/bin/env node
/**
 * Check current session tracker data
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

function checkCurrentTrackerData() {
    console.log('🔍 CHECKING CURRENT SESSION TRACKER DATA');
    console.log('=' .repeat(50));
    
    try {
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        const db = new sqlite3.Database(dbPath);
        
        // Get current session
        db.get('SELECT session_id FROM current_session WHERE id = 1', (err, row) => {
            if (err || !row) {
                console.log('❌ No current session found');
                db.close();
                return;
            }
            
            const sessionId = row.session_id;
            console.log(`📋 Current session: ${sessionId}`);
            
            // Check tracker results for current session
            db.all('SELECT tracker_type, COUNT(*) as count FROM tracker_results WHERE session_id = ? GROUP BY tracker_type', [sessionId], (err, trackerRows) => {
                if (err) {
                    console.error('Error getting tracker data:', err);
                } else if (trackerRows.length > 0) {
                    console.log('\n📊 CURRENT SESSION TRACKER DATA:');
                    trackerRows.forEach(row => {
                        console.log(`   ${row.tracker_type}: ${row.count} records`);
                    });
                } else {
                    console.log('\n📊 No tracker data for current session');
                }
                
                // Check all tracker results (any session)
                db.all('SELECT session_id, tracker_type, COUNT(*) as count FROM tracker_results GROUP BY session_id, tracker_type ORDER BY session_id DESC', (err, allRows) => {
                    if (err) {
                        console.error('Error getting all tracker data:', err);
                    } else if (allRows.length > 0) {
                        console.log('\n📊 ALL TRACKER DATA (BY SESSION):');
                        allRows.forEach(row => {
                            console.log(`   ${row.session_id}: ${row.tracker_type} (${row.count})`);
                        });
                    }
                    
                    // Check if tracker types match expected values
                    db.all('SELECT DISTINCT tracker_type FROM tracker_results', (err, typeRows) => {
                        if (err) {
                            console.error('Error getting tracker types:', err);
                        } else if (typeRows.length > 0) {
                            console.log('\n📊 ALL TRACKER TYPES:');
                            typeRows.forEach(row => {
                                console.log(`   ${row.tracker_type}`);
                            });
                            
                            // Check if types need mapping
                            const expectedTypes = ['in_house_loans', 'external_loans', 'motor_vehicles'];
                            const actualTypes = typeRows.map(r => r.tracker_type);
                            
                            console.log('\n🔍 TYPE MAPPING NEEDED:');
                            actualTypes.forEach(actual => {
                                if (!expectedTypes.includes(actual)) {
                                    console.log(`   ${actual} → needs mapping`);
                                }
                            });
                        }
                        
                        db.close();
                    });
                });
            });
        });
        
    } catch (error) {
        console.error('Error:', error);
    }
}

checkCurrentTrackerData();
