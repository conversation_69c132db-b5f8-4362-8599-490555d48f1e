<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Renderer-Safe UI Coordinator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Renderer-Safe UI Coordinator Test</h1>
        <p>Testing the Electron renderer-safe UI loading coordinator</p>
        
        <button onclick="testCoordinatorLoading()">Test Coordinator Loading</button>
        <button onclick="testMultipleRequests()">Test Multiple Requests</button>
        <button onclick="testRateLimiting()">Test Rate Limiting</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="test-results"></div>
    </div>

    <!-- Mock container for testing -->
    <div class="test-container">
        <h3>Mock UI Container</h3>
        <div id="pre-reporting-interactive-ui" style="border: 1px solid #ccc; padding: 10px; min-height: 100px;">
            <!-- UI will be loaded here -->
        </div>
    </div>

    <!-- Load the renderer-safe coordinator -->
    <script src="core/ui_loading_coordinator_renderer.js"></script>
    
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testCoordinatorLoading() {
            addTestResult('🧪 Testing coordinator loading...', 'info');
            
            try {
                // Check if coordinator is available
                if (!window.UILoadingCoordinator) {
                    addTestResult('❌ UILoadingCoordinator not available', 'error');
                    return;
                }
                
                addTestResult('✅ UILoadingCoordinator class available', 'success');
                
                // Check if getUILoadingCoordinator is available
                if (!window.getUILoadingCoordinator) {
                    addTestResult('❌ getUILoadingCoordinator function not available', 'error');
                    return;
                }
                
                addTestResult('✅ getUILoadingCoordinator function available', 'success');
                
                // Create coordinator instance
                const coordinator = window.getUILoadingCoordinator();
                if (!coordinator) {
                    addTestResult('❌ Failed to create coordinator instance', 'error');
                    return;
                }
                
                addTestResult('✅ Coordinator instance created successfully', 'success');
                
                // Test status
                const status = coordinator.getStatus();
                addTestResult(`✅ Coordinator status: Loading=${status.isLoading}, Queue=${status.queueLength}`, 'success');
                
                // Test container finding
                const container = coordinator.findUIContainer();
                if (container) {
                    addTestResult(`✅ UI container found: ${container.id}`, 'success');
                } else {
                    addTestResult('❌ UI container not found', 'error');
                }
                
                addTestResult('🎉 Coordinator loading test completed successfully!', 'success');
                
            } catch (error) {
                addTestResult(`❌ Coordinator loading test failed: ${error.message}`, 'error');
            }
        }

        async function testMultipleRequests() {
            addTestResult('🧪 Testing multiple simultaneous requests...', 'info');
            
            try {
                const coordinator = window.getUILoadingCoordinator();
                if (!coordinator) {
                    addTestResult('❌ Coordinator not available', 'error');
                    return;
                }
                
                // Mock the API for testing
                window.api = {
                    getLatestPreReportingData: async () => {
                        addTestResult('📡 Mock API called', 'info');
                        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate delay
                        return {
                            success: true,
                            data: [
                                { id: 1, employee_id: 'EMP001', change_type: 'SALARY_INCREASE' },
                                { id: 2, employee_id: 'EMP002', change_type: 'PROMOTION' }
                            ],
                            sessionId: 'test_session_123'
                        };
                    }
                };
                
                // Mock InteractivePreReporting
                window.InteractivePreReporting = class {
                    constructor(container, data) {
                        this.container = container;
                        this.data = data;
                    }
                    
                    async initialize() {
                        this.container.innerHTML = `
                            <div style="background: #e8f5e8; padding: 10px; border-radius: 4px;">
                                ✅ Mock Interactive UI Loaded<br>
                                Records: ${this.data.length}<br>
                                Time: ${new Date().toLocaleTimeString()}
                            </div>
                        `;
                        addTestResult('✅ Mock InteractivePreReporting initialized', 'success');
                    }
                };
                
                // Test multiple simultaneous requests
                addTestResult('🔄 Sending 3 simultaneous requests...', 'info');
                
                const requests = [
                    coordinator.loadPreReportingUI('test_source_1'),
                    coordinator.loadPreReportingUI('test_source_2'),
                    coordinator.loadPreReportingUI('test_source_3')
                ];
                
                const results = await Promise.allSettled(requests);
                
                let successCount = 0;
                results.forEach((result, index) => {
                    if (result.status === 'fulfilled') {
                        successCount++;
                        addTestResult(`✅ Request ${index + 1} succeeded: ${result.value.message}`, 'success');
                    } else {
                        addTestResult(`❌ Request ${index + 1} failed: ${result.reason.message}`, 'error');
                    }
                });
                
                addTestResult(`🎉 Multiple requests test completed: ${successCount}/3 succeeded`, 'success');
                
            } catch (error) {
                addTestResult(`❌ Multiple requests test failed: ${error.message}`, 'error');
            }
        }

        async function testRateLimiting() {
            addTestResult('🧪 Testing rate limiting...', 'info');
            
            try {
                const coordinator = window.getUILoadingCoordinator();
                if (!coordinator) {
                    addTestResult('❌ Coordinator not available', 'error');
                    return;
                }
                
                // Reduce rate limit for testing
                coordinator.minLoadInterval = 2000; // 2 seconds
                
                addTestResult('🔄 Sending rapid requests (should be rate limited)...', 'info');
                
                const startTime = Date.now();
                
                // First request
                const result1 = await coordinator.loadPreReportingUI('rate_test_1');
                const time1 = Date.now() - startTime;
                addTestResult(`✅ First request completed in ${time1}ms`, 'success');
                
                // Second request (should be rate limited)
                const result2 = await coordinator.loadPreReportingUI('rate_test_2');
                const time2 = Date.now() - startTime;
                addTestResult(`✅ Second request completed in ${time2}ms`, 'success');
                
                if (time2 - time1 >= 2000) {
                    addTestResult('✅ Rate limiting working correctly (2+ second delay)', 'success');
                } else {
                    addTestResult('⚠️ Rate limiting may not be working as expected', 'warning');
                }
                
                addTestResult('🎉 Rate limiting test completed!', 'success');
                
            } catch (error) {
                addTestResult(`❌ Rate limiting test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run basic test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('🚀 Page loaded - running basic coordinator test...', 'info');
                testCoordinatorLoading();
            }, 500);
        });
    </script>
</body>
</html>
