/**
 * payroll_audit_core.js
 * Core functionality for the payroll audit process
 */

// Core payroll audit functionality
// Note: auditCurrentPdfPath, auditPreviousPdfPath, enhancedProcessActive, and processStartTime
// are declared in renderer.js to avoid conflicts

/**
 * Start the payroll audit process with selected files
 */
function startPayrollAuditProcess() {
  console.log('🚀🚀🚀 START PAYROLL AUDIT PROCESS CALLED! 🚀🚀🚀');

  // Get file paths from global variables
  const auditCurrentPdfPath = window.auditCurrentPdfPath;
  const auditPreviousPdfPath = window.auditPreviousPdfPath;

  console.log('Current PDF:', auditCurrentPdfPath);
  console.log('Previous PDF:', auditPreviousPdfPath);

  if (!auditCurrentPdfPath || !auditPreviousPdfPath) {
    alert('Please select both current and previous payroll files');
    return;
  }

  // Configuration now handled in Final Report Interface - no validation needed here

  // Get selected months and years
  const currentMonth = document.getElementById('current-month')?.value || '';
  const currentYear = document.getElementById('current-year')?.value || '';
  const previousMonth = document.getElementById('previous-month')?.value || '';
  const previousYear = document.getElementById('previous-year')?.value || '';

  try {
    console.log('Starting backend processing...');

    // CRITICAL: Set process start time for timer
    processStartTime = Date.now();
    enhancedProcessActive = true;

    // Transform UI to processing view
    window.transformToProcessingView(currentMonth, currentYear, previousMonth, previousYear, signatureName, signatureDesignation);

    // Hide start button
    document.getElementById('start-audit-button').classList.add('hidden');
    document.getElementById('stop-audit-button').classList.remove('hidden');

    // Start processing with NEW phased process manager workflow
    window.api.enhancedPayrollAudit(
      auditCurrentPdfPath,
      auditPreviousPdfPath,
      {
        currentMonth,
        currentYear,
        previousMonth,
        previousYear
      }
    ).then(result => {
      console.log('🎉 Phased workflow completed:', result);

      // Use the new workflow completion handler
      if (window.handlePhasedWorkflowCompletion) {
        window.handlePhasedWorkflowCompletion(result);
      }

      // Handle specific error cases
      if (result && !result.success) {
        console.error('❌ Workflow failed:', result.error);
        alert(`Audit process failed: ${result.error}`);

        // Reset UI state
        enhancedProcessActive = false;
        document.getElementById('start-audit-button').classList.remove('hidden');
        document.getElementById('stop-audit-button').classList.add('hidden');
      }
    }).catch(error => {
      console.error('❌ Error during audit workflow:', error);
      alert(`Error during payroll audit: ${error.message}`);

      // Reset UI state
      enhancedProcessActive = false;
      document.getElementById('start-audit-button').classList.remove('hidden');
      document.getElementById('stop-audit-button').classList.add('hidden');
    });

    // Initialize progress listener
    window.initializeProgressListener();

  } catch (error) {
    console.error('Error starting payroll audit:', error);
    alert('Error starting payroll audit. Please check the console for details.');
    window.restoreAuditButtons();
  }
}

/**
 * Stop the payroll audit process
 */
async function stopPayrollAuditProcess() {
  console.log('⛔ STOPPING PAYROLL AUDIT PROCESS');

  try {
    // Call new phased process stop API
    if (window.api.stopProcess) {
      const result = await window.api.stopProcess();
      if (result && result.success) {
        console.log('✅ New phased process stopped successfully');
      }
    }

    // Also call legacy stop API for compatibility
    if (window.api.stopPayrollAudit) {
      window.api.stopPayrollAudit();
    }

    // Update UI
    const stopBtn = document.getElementById('stop-audit-button');
    if (stopBtn) {
      stopBtn.disabled = true;
      stopBtn.textContent = 'Stopping...';
    }

    window.addRealTimeActivity('stopping', 'Stopping process...', null, 'warning');

    // Reset processing variables
    enhancedProcessActive = false;

    // Stop timers
    window.stopProcessingTimer();

    setTimeout(() => {
      window.restoreOriginalView();
      window.restoreAuditButtons();
    }, 1000);

  } catch (error) {
    console.error('Error stopping payroll audit:', error);
    alert('Error stopping payroll audit process.');
    window.restoreAuditButtons();
  }
}

/**
 * Pause the payroll audit process
 */
async function pausePayrollAuditProcess() {
  console.log('⏸️ PAUSING PAYROLL AUDIT PROCESS');

  try {
    const result = await window.api.pauseProcess();

    if (result && result.success) {
      console.log('✅ Process paused successfully');

      // Update UI
      const pauseBtn = document.getElementById('pause-audit-button');
      const resumeBtn = document.getElementById('resume-audit-button');

      if (pauseBtn) pauseBtn.classList.add('hidden');
      if (resumeBtn) resumeBtn.classList.remove('hidden');

      window.addRealTimeActivity('paused', 'Process paused by user', null, 'info');
    } else {
      console.error('❌ Failed to pause process:', result?.error);
    }

  } catch (error) {
    console.error('Error pausing payroll audit:', error);
  }
}

/**
 * Resume the payroll audit process
 */
async function resumePayrollAuditProcess() {
  console.log('▶️ RESUMING PAYROLL AUDIT PROCESS');

  try {
    const result = await window.api.resumeProcess();

    if (result && result.success) {
      console.log('✅ Process resumed successfully');

      // Update UI
      const pauseBtn = document.getElementById('pause-audit-button');
      const resumeBtn = document.getElementById('resume-audit-button');

      if (pauseBtn) pauseBtn.classList.remove('hidden');
      if (resumeBtn) resumeBtn.classList.add('hidden');

      window.addRealTimeActivity('resumed', 'Process resumed by user', null, 'success');
    } else {
      console.error('❌ Failed to resume process:', result?.error);
    }

  } catch (error) {
    console.error('Error resuming payroll audit:', error);
  }
}

/**
 * Restore audit buttons to original state
 */
function restoreAuditButtons() {
  const startBtn = document.getElementById('start-audit-button');
  const stopBtn = document.getElementById('stop-audit-button');
  
  if (startBtn) startBtn.classList.remove('hidden');
  if (stopBtn) {
    stopBtn.classList.add('hidden');
    stopBtn.disabled = false;
    stopBtn.textContent = 'Stop Process';
  }
}

/**
 * Restore original view
 */
function restoreOriginalView() {
  const processingView = document.getElementById('payroll-processing-view');
  const selectionView = document.getElementById('payroll-selection-view');
  
  if (processingView) processingView.classList.add('hidden');
  if (selectionView) selectionView.classList.remove('hidden');
  
  enhancedProcessActive = false;
}

// REMOVED: Duplicate handleAuditFileSelection function
// The working implementation is in renderer.js with correct element IDs
// This duplicate was using wrong element IDs ('current-file-info' vs 'current-payroll-info')

// REMOVED: Duplicate selectAuditFile function
// The working implementation is in renderer.js using window.api.selectPdfFile()
// This duplicate was causing conflicts and using non-existent window.api.openFileDialog()

// REMOVED: Duplicate updateAuditFileInfo function
// The working implementation is in renderer.js with proper HTML styling and display
// This duplicate was only setting textContent (no styling, no display)

// REMOVED: Duplicate checkAuditButtonState function
// The working implementation is in renderer.js with correct button ID 'start-payroll-audit'
// This duplicate was looking for wrong button ID 'start-audit-button'

/**
 * Remove user guide text when files are selected
 */
function removeUserGuideText() {
  const guideText = document.getElementById('guide-text');
  if (guideText) {
    guideText.classList.add('fade-out');
    setTimeout(() => {
      guideText.remove();
    }, 500);
  }
}

// Export functions to be available globally
window.startPayrollAuditProcess = startPayrollAuditProcess;
window.stopPayrollAuditProcess = stopPayrollAuditProcess;
window.pausePayrollAuditProcess = pausePayrollAuditProcess;
window.resumePayrollAuditProcess = resumePayrollAuditProcess;
window.restoreAuditButtons = restoreAuditButtons;
window.restoreOriginalView = restoreOriginalView;
// REMOVED: window.handleAuditFileSelection = handleAuditFileSelection; (duplicate removed, using renderer.js version)
// REMOVED: window.selectAuditFile = selectAuditFile; (duplicate removed, using renderer.js version)
// REMOVED: window.updateAuditFileInfo = updateAuditFileInfo; (duplicate removed, using renderer.js version)
// REMOVED: window.checkAuditButtonState = checkAuditButtonState; (duplicate removed, using renderer.js version)
window.removeUserGuideText = removeUserGuideText;
// Note: auditCurrentPdfPath and auditPreviousPdfPath are managed by renderer.js
