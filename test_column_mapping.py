#!/usr/bin/env python3
"""Test the column mapping layer"""

import sys
import os

def test_column_mapping():
    """Test that column mapping solves inconsistency issues"""
    print("🧪 TESTING COLUMN MAPPING LAYER")
    print("=" * 50)
    
    try:
        # Add the project root to Python path
        sys.path.insert(0, os.path.abspath('.'))
        
        from core.database_column_mapper import get_column_mapper
        from core.database_query_helper import create_query_helper
        from core.python_database_manager import PythonDatabaseManager
        
        # Initialize components
        mapper = get_column_mapper()
        db_manager = PythonDatabaseManager()
        query_helper = create_query_helper(db_manager)
        
        print("✅ Column mapping layer initialized")
        
        # Test 1: Column name mapping
        print(f"\n🔍 TEST 1: COLUMN NAME MAPPING")
        
        test_tables = [
            'comparison_results',
            'pre_reporting_results', 
            'tracker_results',
            'in_house_loans',
            'external_loans'
        ]
        
        for table in test_tables:
            session_col = mapper.get_session_column(table)
            employee_col = mapper.get_employee_column(table)
            category_col = mapper.get_category_column(table)
            
            print(f"   {table}:")
            print(f"     Session: {session_col}")
            print(f"     Employee: {employee_col}")
            print(f"     Category: {category_col}")
        
        # Test 2: Query building
        print(f"\n🔍 TEST 2: STANDARDIZED QUERY BUILDING")
        
        # This should work for ANY table without column name errors
        standard_fields = ['session_ref', 'employee_ref', 'category']
        
        for table in test_tables:
            try:
                query, params = query_helper.get_standardized_query(
                    table, 
                    standard_fields,
                    {'session_ref': 'test_session'}
                )
                print(f"   {table}: ✅ Query built successfully")
                print(f"     Query: {query}")
            except Exception as e:
                print(f"   {table}: ❌ Error - {e}")
        
        # Test 3: Real data access
        print(f"\n🔍 TEST 3: REAL DATA ACCESS WITH MAPPING")
        
        # Get current session
        import sqlite3
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if result:
            session_id = result[0]
            print(f"   Testing with session: {session_id}")
            
            # Test data access from different tables
            test_data_tables = [
                'comparison_results',
                'pre_reporting_results',
                'tracker_results'
            ]
            
            for table in test_data_tables:
                try:
                    count = query_helper.count_session_records(session_id, table)
                    print(f"     {table}: {count} records ✅")
                    
                    if count > 0:
                        # Try to get category breakdown
                        categories = query_helper.get_categories_breakdown(session_id, table)
                        if categories:
                            print(f"       Categories: {len(categories)} types")
                            for cat in categories[:3]:  # Show first 3
                                print(f"         {cat['category']}: {cat['count']}")
                        
                except Exception as e:
                    print(f"     {table}: ❌ Error - {e}")
        else:
            print("   ❌ No current session found")
        
        conn.close()
        
        # Test 4: Column mapping benefits
        print(f"\n🎉 COLUMN MAPPING BENEFITS:")
        print("✅ NO MORE 'no such column' errors")
        print("✅ Consistent data access across all tables")
        print("✅ Automatic handling of column name differences")
        print("✅ Standardized field names in code")
        print("✅ Easy to add new tables with different schemas")
        
        print(f"\n💡 HOW TO USE:")
        print("1. Import: from core.database_query_helper import create_query_helper")
        print("2. Initialize: query_helper = create_query_helper(db_manager)")
        print("3. Use standardized methods:")
        print("   - query_helper.count_session_records(session_id, table_name)")
        print("   - query_helper.get_categories_breakdown(session_id, table_name)")
        print("   - query_helper.execute_standardized_query(table, fields, conditions)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing column mapping: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_column_mapping()
    
    if success:
        print(f"\n🎯 NEXT STEPS:")
        print("1. Replace direct database queries with query_helper methods")
        print("2. Use standardized field names everywhere")
        print("3. No more column name mismatches!")
    else:
        print(f"\n💡 TROUBLESHOOTING:")
        print("1. Check database connection")
        print("2. Verify table structures")
        print("3. Review mapping definitions")
