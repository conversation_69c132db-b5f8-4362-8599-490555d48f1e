/**
 * Process Coordinator
 * Prevents SIGTERM by coordinating all process spawning and database access
 */

class ProcessCoordinator {
    constructor() {
        this.activeProcesses = new Map();
        this.processQueue = [];
        this.maxConcurrentProcesses = 1; // Only one process at a time
        this.isShuttingDown = false;
        this.lastProcessTime = 0;
        this.minProcessInterval = 3000; // 3 seconds between processes
    }

    async executeProcess(processType, command, args = [], options = {}) {
        const processId = `${processType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        console.log(`[PROCESS-COORDINATOR] Process request: ${processType} (ID: ${processId})`);

        // Check if we're shutting down
        if (this.isShuttingDown) {
            throw new Error('Process coordinator is shutting down');
        }

        // Check if we can execute immediately
        if (this.activeProcesses.size < this.maxConcurrentProcesses) {
            return this.startProcess(processId, processType, command, args, options);
        } else {
            // Queue the process
            console.log(`[PROCESS-COORDINATOR] Queuing process: ${processType}`);
            return new Promise((resolve, reject) => {
                this.processQueue.push({
                    processId,
                    processType,
                    command,
                    args,
                    options,
                    resolve,
                    reject,
                    timestamp: Date.now()
                });
                
                // Set timeout for queued process
                setTimeout(() => {
                    const queueIndex = this.processQueue.findIndex(p => p.processId === processId);
                    if (queueIndex !== -1) {
                        this.processQueue.splice(queueIndex, 1);
                        reject(new Error(`Process timeout for ${processType}`));
                    }
                }, 30000); // 30 second timeout
            });
        }
    }

    async startProcess(processId, processType, command, args, options) {
        // Check minimum interval
        const timeSinceLastProcess = Date.now() - this.lastProcessTime;
        if (timeSinceLastProcess < this.minProcessInterval) {
            const waitTime = this.minProcessInterval - timeSinceLastProcess;
            console.log(`[PROCESS-COORDINATOR] Rate limiting: waiting ${waitTime}ms before ${processType}`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        this.lastProcessTime = Date.now();

        console.log(`[PROCESS-COORDINATOR] Starting process: ${processType}`);

        try {
            // Mark process as active
            this.activeProcesses.set(processId, {
                type: processType,
                startTime: Date.now(),
                command,
                args
            });

            // Execute the process
            const result = await this.executeActualProcess(command, args, options);
            
            console.log(`[PROCESS-COORDINATOR] Process completed: ${processType}`);
            return result;

        } catch (error) {
            console.error(`[PROCESS-COORDINATOR] Process failed: ${processType}:`, error);
            throw error;

        } finally {
            // Remove from active processes
            this.activeProcesses.delete(processId);
            
            // Process next queued item
            this.processQueue();
        }
    }

    async executeActualProcess(command, args, options) {
        // This would be implemented based on your process execution needs
        // For now, return a mock result
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({ success: true, message: 'Process completed' });
            }, 1000);
        });
    }

    processQueue() {
        if (this.processQueue.length > 0 && this.activeProcesses.size < this.maxConcurrentProcesses) {
            const nextProcess = this.processQueue.shift();
            console.log(`[PROCESS-COORDINATOR] Processing queued: ${nextProcess.processType}`);
            
            this.startProcess(
                nextProcess.processId,
                nextProcess.processType,
                nextProcess.command,
                nextProcess.args,
                nextProcess.options
            ).then(nextProcess.resolve).catch(nextProcess.reject);
        }
    }

    async killAllProcesses() {
        console.log('[PROCESS-COORDINATOR] Killing all active processes...');
        this.isShuttingDown = true;

        // Clear queue
        this.processQueue.forEach(process => {
            process.reject(new Error('Process coordinator shutting down'));
        });
        this.processQueue = [];

        // Kill active processes
        const killPromises = Array.from(this.activeProcesses.keys()).map(processId => {
            return this.killProcess(processId);
        });

        await Promise.all(killPromises);
        
        this.activeProcesses.clear();
        console.log('[PROCESS-COORDINATOR] All processes killed');
    }

    async killProcess(processId) {
        const process = this.activeProcesses.get(processId);
        if (process) {
            console.log(`[PROCESS-COORDINATOR] Killing process: ${process.type}`);
            // Implementation would depend on how processes are actually managed
            this.activeProcesses.delete(processId);
        }
    }

    getStatus() {
        return {
            activeProcesses: this.activeProcesses.size,
            queuedProcesses: this.processQueue.length,
            isShuttingDown: this.isShuttingDown,
            activeProcessTypes: Array.from(this.activeProcesses.values()).map(p => p.type)
        };
    }

    // Prevent specific problematic processes
    shouldBlockProcess(processType, command) {
        const blockedPatterns = [
            'bank_adviser_tracker_operations.py',
            'populate_tables',
            'tracker-population'
        ];

        return blockedPatterns.some(pattern => 
            processType.includes(pattern) || 
            (command && command.includes(pattern))
        );
    }

    async executeIfNotBlocked(processType, command, args = [], options = {}) {
        if (this.shouldBlockProcess(processType, command)) {
            console.log(`[PROCESS-COORDINATOR] Blocking problematic process: ${processType}`);
            return { success: false, blocked: true, reason: 'Process blocked to prevent conflicts' };
        }

        return this.executeProcess(processType, command, args, options);
    }
}

// Global instance
let globalProcessCoordinator = null;

function getProcessCoordinator() {
    if (!globalProcessCoordinator) {
        globalProcessCoordinator = new ProcessCoordinator();
    }
    return globalProcessCoordinator;
}

// Export for Electron environment
if (typeof window !== 'undefined') {
    // Electron renderer process
    window.ProcessCoordinator = ProcessCoordinator;
    window.getProcessCoordinator = getProcessCoordinator;
    console.log('[PROCESS-COORDINATOR] Loaded in Electron renderer process');
}

// Only export for Node.js if we're actually in Node.js main process
if (typeof module !== 'undefined' && typeof window === 'undefined') {
    module.exports = { ProcessCoordinator, getProcessCoordinator };
}
