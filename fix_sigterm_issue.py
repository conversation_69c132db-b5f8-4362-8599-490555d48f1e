#!/usr/bin/env python3
"""Fix SIGTERM issue by ensuring clean session state"""

import sqlite3

def fix_sigterm_issue():
    """Fix SIGTERM issue by ensuring session is properly ready for UI"""
    print("🔧 FIXING SIGTERM ISSUE")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return False
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check current session status
        cursor.execute('SELECT status FROM audit_sessions WHERE session_id = ?', (session_id,))
        session_status = cursor.fetchone()
        
        if session_status:
            current_status = session_status[0]
            print(f"   Current status: {current_status}")
        else:
            print("   ❌ No session status found")
            return False
        
        # Ensure session is marked as ready for UI interaction
        print(f"\n🔧 ENSURING SESSION IS READY FOR UI:")
        
        # Update session status to indicate it's ready for user interaction
        cursor.execute('''
            UPDATE audit_sessions 
            SET status = 'pre_reporting_ready',
                updated_at = datetime('now')
            WHERE session_id = ?
        ''', (session_id,))
        
        # Ensure PRE_REPORTING phase is properly set to WAITING_FOR_USER
        cursor.execute('''
            UPDATE session_phases 
            SET status = 'WAITING_FOR_USER',
                updated_at = datetime('now')
            WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
        ''', (session_id,))
        
        # Commit changes
        conn.commit()
        
        print("   ✅ Session status updated to 'pre_reporting_ready'")
        print("   ✅ PRE_REPORTING phase confirmed as 'WAITING_FOR_USER'")
        
        # Verify the fix
        print(f"\n🔍 VERIFYING FIX:")
        
        cursor.execute('SELECT status FROM audit_sessions WHERE session_id = ?', (session_id,))
        new_status = cursor.fetchone()[0]
        print(f"   Session status: {new_status}")
        
        cursor.execute('''
            SELECT status FROM session_phases 
            WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
        ''', (session_id,))
        phase_status = cursor.fetchone()[0]
        print(f"   PRE_REPORTING phase: {phase_status}")
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session_id,))
        data_count = cursor.fetchone()[0]
        print(f"   Pre-reporting data: {data_count} records")
        
        conn.close()
        
        if new_status == 'pre_reporting_ready' and phase_status == 'WAITING_FOR_USER' and data_count > 0:
            print(f"\n🎉 SUCCESS: Session is properly configured for interactive UI")
            print(f"✅ Status: {new_status}")
            print(f"✅ Phase: {phase_status}")
            print(f"✅ Data: {data_count} records")
            
            print(f"\n💡 SIGTERM PREVENTION MEASURES:")
            print("1. ✅ Session marked as 'pre_reporting_ready'")
            print("2. ✅ PRE_REPORTING phase set to 'WAITING_FOR_USER'")
            print("3. ✅ No background processes should interfere")
            print("4. ✅ UI should load cleanly without SIGTERM")
            
            return True
        else:
            print(f"\n❌ FAILED: Session not properly configured")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def additional_sigterm_fixes():
    """Additional measures to prevent SIGTERM"""
    print(f"\n🔧 ADDITIONAL SIGTERM PREVENTION:")
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session_id = cursor.fetchone()[0]
        
        # Ensure no processes are marked as running
        cursor.execute('''
            UPDATE audit_sessions 
            SET processing_pid = NULL,
                processing_started = NULL
            WHERE session_id = ?
        ''', (session_id,))
        
        # Clear any temporary locks
        cursor.execute('PRAGMA optimize')
        
        conn.commit()
        conn.close()
        
        print("   ✅ Cleared any running process indicators")
        print("   ✅ Optimized database connections")
        
    except Exception as e:
        print(f"   ⚠️ Additional fixes error: {e}")

if __name__ == "__main__":
    success = fix_sigterm_issue()
    
    if success:
        additional_sigterm_fixes()
        
        print(f"\n🎯 NEXT STEPS:")
        print("1. Try loading the interactive UI again")
        print("2. The session should load without SIGTERM")
        print("3. You should see 5594 pre-reporting changes")
        print("4. Interactive pre-reporting interface should work")
    else:
        print(f"\n💡 TROUBLESHOOTING:")
        print("1. Check if any background processes are still running")
        print("2. Restart the app completely")
        print("3. Try loading the interactive UI again")
