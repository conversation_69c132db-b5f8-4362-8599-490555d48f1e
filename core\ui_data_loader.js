/**
 * UI Data Loader with Column Mapping Integration
 * Prevents SIGTERM by handling column name inconsistencies before UI loads
 */

const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Import the column mapper (we'll need to adapt this for Node.js)
class UIColumnMapper {
    constructor() {
        // Define column mappings for UI data loading
        this.tableMappings = {
            'comparison_results': {
                'session_ref': 'session_id',
                'employee_ref': 'employee_id',
                'section': 'section_name',
                'category': 'section_name',
                'item': 'item_label',
                'value': 'current_value',
                'previous_value': 'previous_value',
                'change_type': 'change_type',
                'priority': 'priority'
            },
            'pre_reporting_results': {
                'session_ref': 'session_id',
                'employee_ref': 'change_id',
                'section': 'bulk_category',
                'category': 'bulk_category',
                'item': 'change_id',
                'value': 'bulk_size',
                'selected': 'selected_for_report'
            }
        };
    }

    getColumnName(tableName, standardField) {
        if (!this.tableMappings[tableName]) {
            return standardField;
        }
        return this.tableMappings[tableName][standardField] || standardField;
    }

    buildSafeQuery(sessionId) {
        // Build a query using mapped column names to prevent column errors
        const crTable = 'comparison_results';
        const prTable = 'pre_reporting_results';
        
        // Get actual column names
        const crSessionCol = this.getColumnName(crTable, 'session_ref');
        const crEmployeeCol = this.getColumnName(crTable, 'employee_ref');
        const crSectionCol = this.getColumnName(crTable, 'section');
        const crItemCol = this.getColumnName(crTable, 'item');
        const crValueCol = this.getColumnName(crTable, 'value');
        const crPrevValueCol = this.getColumnName(crTable, 'previous_value');
        const crChangeTypeCol = this.getColumnName(crTable, 'change_type');
        const crPriorityCol = this.getColumnName(crTable, 'priority');
        
        const prSessionCol = this.getColumnName(prTable, 'session_ref');
        const prSelectedCol = this.getColumnName(prTable, 'selected');

        // Build safe query with mapped column names
        const query = `
            SELECT cr.id, 
                   cr.${crEmployeeCol} as employee_id, 
                   cr.employee_name, 
                   cr.${crSectionCol} as section_name, 
                   cr.${crItemCol} as item_label,
                   cr.${crPrevValueCol} as previous_value, 
                   cr.${crValueCol} as current_value, 
                   cr.${crChangeTypeCol} as change_type, 
                   cr.${crPriorityCol} as priority,
                   cr.numeric_difference, 
                   cr.percentage_change,
                   COALESCE(pr.${prSelectedCol}, 1) as selected
            FROM ${crTable} cr
            LEFT JOIN ${prTable} pr ON cr.id = pr.change_id AND cr.${crSessionCol} = pr.${prSessionCol}
            LEFT JOIN dictionary_items di ON cr.${crItemCol} = di.item_name
            WHERE cr.${crSessionCol} = ?
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
            ORDER BY cr.${crPriorityCol} DESC, cr.${crEmployeeCol}, cr.${crSectionCol}, cr.${crItemCol}
        `;

        return query;
    }
}

class UIDataLoader {
    constructor() {
        this.mapper = new UIColumnMapper();
    }

    async getPreReportingDataSafely(sessionId = null) {
        return new Promise((resolve, reject) => {
            console.log('[UI-DATA-LOADER] Loading pre-reporting data with column mapping...');

            const dbPath = path.join(__dirname, '..', 'data', 'templar_payroll_auditor.db');
            const db = new sqlite3.Database(dbPath);

            // Get current session if not provided
            if (!sessionId) {
                db.get('SELECT session_id FROM current_session WHERE id = 1', (err, row) => {
                    if (err || !row) {
                        db.close();
                        resolve({ success: false, error: 'No current session found' });
                        return;
                    }
                    
                    sessionId = row.session_id;
                    console.log(`[UI-DATA-LOADER] Using session: ${sessionId}`);
                    this.executeDataQuery(db, sessionId, resolve);
                });
            } else {
                this.executeDataQuery(db, sessionId, resolve);
            }
        });
    }

    executeDataQuery(db, sessionId, resolve) {
        try {
            // Build safe query using column mapper
            const safeQuery = this.mapper.buildSafeQuery(sessionId);
            
            console.log('[UI-DATA-LOADER] Executing mapped query...');
            console.log('[UI-DATA-LOADER] Query preview:', safeQuery.substring(0, 200) + '...');

            db.all(safeQuery, [sessionId], (err, rows) => {
                db.close();

                if (err) {
                    console.error('[UI-DATA-LOADER] Query error:', err);
                    resolve({ success: false, error: `Database query failed: ${err.message}` });
                    return;
                }

                console.log(`[UI-DATA-LOADER] Successfully retrieved ${rows.length} records`);
                
                // Process and standardize the data
                const processedData = this.processDataForUI(rows);
                
                resolve({
                    success: true,
                    data: processedData,
                    sessionId: sessionId,
                    count: processedData.length
                });
            });

        } catch (error) {
            db.close();
            console.error('[UI-DATA-LOADER] Query building error:', error);
            resolve({ success: false, error: `Query building failed: ${error.message}` });
        }
    }

    processDataForUI(rows) {
        // Process and standardize data for UI consumption
        return rows.map(row => ({
            id: row.id,
            employee_id: row.employee_id,
            employee_name: row.employee_name,
            section_name: row.section_name,
            item_label: row.item_label,
            previous_value: row.previous_value,
            current_value: row.current_value,
            change_type: row.change_type,
            priority: row.priority,
            numeric_difference: row.numeric_difference,
            percentage_change: row.percentage_change,
            selected: row.selected === 1
        }));
    }

    async testConnection() {
        console.log('[UI-DATA-LOADER] Testing database connection and column mapping...');
        
        try {
            const result = await this.getPreReportingDataSafely();
            
            if (result.success) {
                console.log(`[UI-DATA-LOADER] ✅ Connection test successful: ${result.count} records`);
                return { success: true, count: result.count };
            } else {
                console.log(`[UI-DATA-LOADER] ❌ Connection test failed: ${result.error}`);
                return { success: false, error: result.error };
            }
        } catch (error) {
            console.log(`[UI-DATA-LOADER] ❌ Connection test error: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

module.exports = { UIDataLoader, UIColumnMapper };
