#!/usr/bin/env python3
"""Debug external loan classification specifically"""

import sqlite3
import sys
import os

def debug_external_classification():
    """Debug why external loans aren't being classified correctly"""
    print("🔍 DEBUGGING EXTERNAL LOAN CLASSIFICATION")
    print("=" * 50)
    
    try:
        # Add the project root to Python path
        sys.path.insert(0, os.path.abspath('.'))
        
        from core.phased_process_manager import PhasedProcessManager
        from core.python_database_manager import PythonDatabaseManager
        
        # Get current session
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Initialize manager
        db_manager = PythonDatabaseManager()
        manager = PhasedProcessManager(debug_mode=True)
        manager.db_manager = db_manager
        manager.session_id = session_id
        
        # Load in-house loan types
        print(f"\n🔍 LOADING IN-HOUSE LOAN TYPES:")
        in_house_types = manager._load_in_house_loan_types()
        print(f"   In-house loan types: {sorted(in_house_types)}")
        
        # Test specific external loan items
        print(f"\n🔍 TESTING EXTERNAL LOAN CLASSIFICATION:")
        
        external_test_items = [
            {
                'employee_id': 'TEST001',
                'employee_name': 'Test Employee',
                'section_name': 'LOANS',
                'item_label': 'GCB BANK  LIBERTY HSE - BALANCE B/F',
                'item_value': '50000.00'
            },
            {
                'employee_id': 'TEST002', 
                'employee_name': 'Test Employee 2',
                'section_name': 'LOANS',
                'item_label': 'OMNI BANK LOAN - BALANCE B/F',
                'item_value': '25000.00'
            },
            {
                'employee_id': 'TEST003',
                'employee_name': 'Test Employee 3', 
                'section_name': 'LOANS',
                'item_label': 'GLICO LIFE INSURANCE C - BALANCE B/F',
                'item_value': '15000.00'
            },
            {
                'employee_id': 'TEST004',
                'employee_name': 'Test Employee 4',
                'section_name': 'LOANS', 
                'item_label': 'ECOBANK SCHEME LOAN - BALANCE B/F',
                'item_value': '30000.00'
            }
        ]
        
        for item in external_test_items:
            print(f"\n   Testing: {item['item_label']}")
            
            # Check if trackable
            is_trackable = manager._is_trackable_item(item)
            print(f"     Is trackable: {is_trackable}")
            
            if is_trackable:
                # Classify tracker type
                tracker_type = manager._classify_tracker_type(item, in_house_types)
                print(f"     Tracker type: {tracker_type}")
                
                if tracker_type == 'external_loans':
                    print(f"     ✅ CORRECTLY classified as external loan")
                elif tracker_type == 'in_house_loans':
                    print(f"     ❌ INCORRECTLY classified as in-house loan")
                else:
                    print(f"     ❌ UNEXPECTED classification: {tracker_type}")
            else:
                print(f"     ❌ NOT TRACKABLE")
        
        # Check actual NEW external loan items from database
        print(f"\n🔍 CHECKING ACTUAL NEW EXTERNAL LOAN ITEMS:")
        
        cursor.execute('''
            SELECT employee_id, employee_name, section_name, item_label, current_value
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW' AND (
                item_label LIKE '%GCB BANK%BALANCE B/F' OR
                item_label LIKE '%OMNI BANK%BALANCE B/F' OR
                item_label LIKE '%GLICO%BALANCE B/F' OR
                item_label LIKE '%ECOBANK%BALANCE B/F'
            )
            LIMIT 10
        ''', (session_id,))
        
        actual_items = cursor.fetchall()
        if actual_items:
            print("   Actual NEW external loan items:")
            for row in actual_items:
                item = {
                    'employee_id': row[0],
                    'employee_name': row[1], 
                    'section_name': row[2],
                    'item_label': row[3],
                    'item_value': row[4]
                }
                
                print(f"     {item['item_label']} = {item['item_value']}")
                
                # Test classification
                is_trackable = manager._is_trackable_item(item)
                if is_trackable:
                    tracker_type = manager._classify_tracker_type(item, in_house_types)
                    print(f"       → Classified as: {tracker_type}")
                else:
                    print(f"       → NOT TRACKABLE")
        else:
            print("   ❌ No actual NEW external loan items found")
        
        conn.close()
        
        print(f"\n💡 ANALYSIS:")
        print("1. Check if external loan items are being filtered out")
        print("2. Verify classification logic works correctly")
        print("3. Ensure Balance B/F items are being processed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_external_classification()
