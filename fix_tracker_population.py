#!/usr/bin/env python3
"""Fix tracker table population by re-running tracker feeding with corrected storage"""

import sqlite3
import sys
import os

def fix_tracker_population():
    """Re-run tracker feeding to populate Bank Adviser tables correctly"""
    print("🔧 FIXING TRACKER TABLE POPULATION")
    print("=" * 50)
    
    try:
        # Add the project root to Python path
        sys.path.insert(0, os.path.abspath('.'))
        
        from core.phased_process_manager import PhasedProcessManager
        from core.python_database_manager import PythonDatabaseManager
        
        # Get current session
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return False
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check current tracker table status
        print(f"\n🔍 BEFORE FIX - TRACKER TABLE STATUS:")
        
        tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table in tracker_tables:
            cursor.execute(f'SELECT COUNT(*) FROM {table} WHERE source_session = ?', (session_id,))
            count = cursor.fetchone()[0]
            print(f"   {table}: {count} records")
        
        cursor.execute('SELECT COUNT(*) FROM tracker_results WHERE session_id = ?', (session_id,))
        tracker_results_count = cursor.fetchone()[0]
        print(f"   tracker_results: {tracker_results_count} records")
        
        conn.close()
        
        # Initialize phased process manager
        print(f"\n🔧 INITIALIZING TRACKER FEEDING FIX:")
        
        db_manager = PythonDatabaseManager()
        manager = PhasedProcessManager(debug_mode=True)
        manager.db_manager = db_manager
        manager.session_id = session_id
        
        # Clear existing tracker table data for this session
        print(f"\n🧹 CLEARING EXISTING TRACKER DATA:")
        
        for table in tracker_tables:
            try:
                manager.db_manager.execute_update(
                    f'DELETE FROM {table} WHERE source_session = ?',
                    (session_id,)
                )
                print(f"   ✅ Cleared {table}")
            except Exception as e:
                print(f"   ⚠️ Could not clear {table}: {e}")
        
        # Re-run tracker feeding phase
        print(f"\n🔄 RE-RUNNING TRACKER FEEDING:")
        
        options = {}
        success = manager._phase_tracker_feeding(options)
        
        if success:
            print("✅ Tracker feeding completed successfully")
            
            # Check results
            print(f"\n🔍 AFTER FIX - TRACKER TABLE STATUS:")
            
            conn = sqlite3.connect('data/templar_payroll_auditor.db')
            cursor = conn.cursor()
            
            total_tracked = 0
            for table in tracker_tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table} WHERE source_session = ?', (session_id,))
                count = cursor.fetchone()[0]
                total_tracked += count
                print(f"   {table}: {count} records")
            
            cursor.execute('SELECT COUNT(*) FROM tracker_results WHERE session_id = ?', (session_id,))
            tracker_results_count = cursor.fetchone()[0]
            print(f"   tracker_results: {tracker_results_count} records")
            
            conn.close()
            
            if total_tracked > 0:
                print(f"\n🎉 SUCCESS: {total_tracked} items now properly tracked in Bank Adviser tables!")
                print("✅ Bank Adviser UI should now show tracker data")
            else:
                print(f"\n⚠️ WARNING: No items were tracked to Bank Adviser tables")
                print("💡 This might be due to filtering or classification issues")
            
            return True
            
        else:
            print("❌ Tracker feeding failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_tracker_population()
    
    if success:
        print("\n💡 NEXT STEPS:")
        print("1. Refresh your Bank Adviser tab")
        print("2. Check tracker data in the UI")
        print("3. Verify loan and motor vehicle data appears")
    else:
        print("\n💡 TROUBLESHOOTING:")
        print("1. Check if NEW items exist in comparison_results")
        print("2. Verify dictionary settings allow tracking")
        print("3. Check item classification logic")
