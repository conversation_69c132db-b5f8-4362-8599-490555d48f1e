<!DOCTYPE html>
<html>
<head>
    <title>Complete File Selection Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .btn { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; }
        .btn.success { background: #28a745; }
        .btn:disabled { background: #6c757d; opacity: 0.6; cursor: not-allowed; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; font-family: monospace; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        .warning { background: #fff3cd; }
        
        /* Test file info styling */
        .file-info {
            margin-top: 15px;
            padding: 12px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            color: #155724;
            font-size: 14px;
            display: none;
            min-height: 20px;
        }
        
        .file-info[style*="display: block"] {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
        }
        
        .file-info i {
            margin-right: 8px;
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>🧪 Complete File Selection Test</h1>
    
    <div class="test-section">
        <h3>1. Simulate Payroll Audit Interface</h3>
        
        <!-- Current Payroll Section -->
        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd;">
            <h4>Current Month Payroll</h4>
            <button id="browse-current-payroll" class="btn">Browse Files</button>
            <div id="current-payroll-info" class="file-info">Current file info will appear here</div>
        </div>
        
        <!-- Previous Payroll Section -->
        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd;">
            <h4>Previous Month Payroll</h4>
            <button id="browse-previous-payroll" class="btn">Browse Files</button>
            <div id="previous-payroll-info" class="file-info">Previous file info will appear here</div>
        </div>
        
        <!-- Start Button -->
        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd;">
            <button id="start-payroll-audit" class="btn" disabled>Start Payroll Audit</button>
            <div id="button-status" class="result">Button status will appear here</div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>2. Test Controls</h3>
        <button id="test-current-selection" class="btn">Test Current File Selection</button>
        <button id="test-previous-selection" class="btn">Test Previous File Selection</button>
        <button id="test-both-selections" class="btn">Test Both File Selections</button>
        <button id="reset-test" class="btn">Reset Test</button>
        <div id="test-result" class="result">Test results will appear here</div>
    </div>

    <script>
        // Mock variables
        let auditCurrentPdfPath = null;
        let auditPreviousPdfPath = null;
        
        // Enhanced updateAuditFileInfo function
        function updateAuditFileInfo(elementId, filePath) {
            console.log(`🔄 Updating file info for element: ${elementId}, file: ${filePath}`);
            
            const infoElement = document.getElementById(elementId);
            if (infoElement) {
                const fileName = filePath.split('\\').pop().split('/').pop();
                infoElement.innerHTML = `<i class="fas fa-check-circle"></i> ${fileName}`;
                
                // Force display with multiple methods
                infoElement.style.display = 'block';
                infoElement.style.visibility = 'visible';
                infoElement.style.opacity = '1';
                infoElement.style.height = 'auto';
                infoElement.classList.remove('hidden');
                
                console.log(`✅ File info updated: ${fileName}`);
                return true;
            } else {
                console.error(`❌ Element not found: ${elementId}`);
                return false;
            }
        }
        
        // Enhanced checkAuditButtonState function
        function checkAuditButtonState() {
            const auditBtn = document.getElementById('start-payroll-audit');
            const statusDiv = document.getElementById('button-status');
            
            if (!auditBtn) {
                console.log('❌ Start audit button not found');
                return;
            }
            
            const hasCurrentPdf = !!(auditCurrentPdfPath || window.auditCurrentPdfPath);
            const hasPreviousPdf = !!(auditPreviousPdfPath || window.auditPreviousPdfPath);
            const allRequirementsMet = hasCurrentPdf && hasPreviousPdf;
            
            console.log('🔍 Enhanced button state check:');
            console.log('  Has Current PDF:', hasCurrentPdf);
            console.log('  Has Previous PDF:', hasPreviousPdf);
            console.log('  All requirements met:', allRequirementsMet);
            
            // Update status display
            statusDiv.innerHTML = `
                Current PDF: ${hasCurrentPdf ? '✅' : '❌'}<br>
                Previous PDF: ${hasPreviousPdf ? '✅' : '❌'}<br>
                Button State: ${allRequirementsMet ? 'ENABLED' : 'DISABLED'}
            `;
            
            auditBtn.disabled = !allRequirementsMet;
            
            if (allRequirementsMet) {
                auditBtn.style.opacity = '1';
                auditBtn.style.cursor = 'pointer';
                auditBtn.classList.remove('disabled');
                auditBtn.style.backgroundColor = '#28a745'; // Green
                auditBtn.style.color = 'white';
                auditBtn.style.boxShadow = '0 4px 12px rgba(40, 167, 69, 0.3)';
                statusDiv.className = 'result success';
                console.log('✅ Button ENABLED (GREEN)');
            } else {
                auditBtn.style.opacity = '0.6';
                auditBtn.style.cursor = 'not-allowed';
                auditBtn.classList.add('disabled');
                auditBtn.style.backgroundColor = '#6c757d';
                auditBtn.style.color = 'white';
                auditBtn.style.boxShadow = 'none';
                statusDiv.className = 'result warning';
                console.log('❌ Button DISABLED');
            }
        }
        
        // Test functions
        function testFileSelection(type) {
            const testPath = `C:\\Test\\${type}-payroll-sample.pdf`;
            const result = document.getElementById('test-result');
            
            result.innerHTML = `🔄 Testing ${type} file selection...`;
            result.className = 'result warning';
            
            // Simulate file selection
            if (type === 'current') {
                auditCurrentPdfPath = testPath;
                window.auditCurrentPdfPath = testPath;
                const success = updateAuditFileInfo('current-payroll-info', testPath);
                
                if (success) {
                    result.innerHTML = `✅ ${type} file selection test PASSED`;
                    result.className = 'result success';
                } else {
                    result.innerHTML = `❌ ${type} file selection test FAILED`;
                    result.className = 'result error';
                }
            } else {
                auditPreviousPdfPath = testPath;
                window.auditPreviousPdfPath = testPath;
                const success = updateAuditFileInfo('previous-payroll-info', testPath);
                
                if (success) {
                    result.innerHTML = `✅ ${type} file selection test PASSED`;
                    result.className = 'result success';
                } else {
                    result.innerHTML = `❌ ${type} file selection test FAILED`;
                    result.className = 'result error';
                }
            }
            
            checkAuditButtonState();
        }
        
        function resetTest() {
            auditCurrentPdfPath = null;
            auditPreviousPdfPath = null;
            window.auditCurrentPdfPath = null;
            window.auditPreviousPdfPath = null;
            
            document.getElementById('current-payroll-info').style.display = 'none';
            document.getElementById('previous-payroll-info').style.display = 'none';
            
            checkAuditButtonState();
            
            document.getElementById('test-result').innerHTML = 'Test reset complete';
            document.getElementById('test-result').className = 'result';
        }
        
        // Event listeners
        document.getElementById('test-current-selection').addEventListener('click', () => testFileSelection('current'));
        document.getElementById('test-previous-selection').addEventListener('click', () => testFileSelection('previous'));
        document.getElementById('test-both-selections').addEventListener('click', () => {
            testFileSelection('current');
            setTimeout(() => testFileSelection('previous'), 500);
        });
        document.getElementById('reset-test').addEventListener('click', resetTest);
        
        // Initialize
        checkAuditButtonState();
        
        console.log('✅ Complete file selection test loaded');
    </script>
</body>
</html>
