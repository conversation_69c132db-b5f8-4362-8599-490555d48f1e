#!/usr/bin/env node
/**
 * Count Individual Anomalies
 * Analyze the 78,483 comparison results to count individual anomalies by priority
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function countIndividualAnomalies() {
    console.log('🔍 COUNTING INDIVIDUAL ANOMALIES');
    console.log('=' .repeat(60));
    
    try {
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        const db = new sqlite3.Database(dbPath);
        
        // Get current session
        const currentSession = await new Promise((resolve, reject) => {
            db.get('SELECT session_id FROM current_session WHERE id = 1', (err, row) => {
                if (err) reject(err);
                else resolve(row ? row.session_id : null);
            });
        });
        
        if (!currentSession) {
            console.log('❌ No current session found');
            db.close();
            return;
        }
        
        console.log(`📋 Current session: ${currentSession}`);
        
        // Get all comparison results
        const allResults = await new Promise((resolve, reject) => {
            db.all(`
                SELECT 
                    employee_id,
                    employee_name,
                    section_name,
                    item_label,
                    change_type,
                    current_value,
                    previous_value
                FROM comparison_results 
                WHERE session_id = ?
                ORDER BY employee_id, section_name, item_label
            `, [currentSession], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
        
        console.log(`📊 Total comparison results: ${allResults.length}`);
        
        if (allResults.length === 0) {
            console.log('❌ No comparison results found');
            db.close();
            return;
        }
        
        // Group changes by item (section + item_label) to identify bulk vs individual
        const itemGroups = {};
        
        allResults.forEach(change => {
            const key = `${change.section_name}|${change.item_label}`;
            
            if (!itemGroups[key]) {
                itemGroups[key] = {
                    section: change.section_name,
                    item: change.item_label,
                    changes: [],
                    employees: new Set()
                };
            }
            
            itemGroups[key].changes.push(change);
            itemGroups[key].employees.add(change.employee_id);
        });
        
        // Determine priority for each section
        function determinePriority(sectionName) {
            const normalizedSection = sectionName.toLowerCase();
            
            if (normalizedSection.includes('personal') || 
                normalizedSection.includes('earnings') || 
                normalizedSection.includes('deductions') || 
                normalizedSection.includes('bank')) {
                return 'HIGH';
            } else if (normalizedSection.includes('loan')) {
                return 'MODERATE';
            } else {
                return 'LOW';
            }
        }
        
        // Categorize by bulk size and priority
        const categories = {
            'INDIVIDUAL_HIGH': { changes: [], employees: new Set() },
            'INDIVIDUAL_MODERATE': { changes: [], employees: new Set() },
            'INDIVIDUAL_LOW': { changes: [], employees: new Set() },
            'SMALL_BULK_HIGH': { changes: [], employees: new Set() },
            'SMALL_BULK_MODERATE': { changes: [], employees: new Set() },
            'SMALL_BULK_LOW': { changes: [], employees: new Set() },
            'MEDIUM_BULK_HIGH': { changes: [], employees: new Set() },
            'MEDIUM_BULK_MODERATE': { changes: [], employees: new Set() },
            'MEDIUM_BULK_LOW': { changes: [], employees: new Set() },
            'LARGE_BULK_HIGH': { changes: [], employees: new Set() },
            'LARGE_BULK_MODERATE': { changes: [], employees: new Set() },
            'LARGE_BULK_LOW': { changes: [], employees: new Set() }
        };
        
        // Categorize each item group
        Object.values(itemGroups).forEach(group => {
            const employeeCount = group.employees.size;
            const priority = determinePriority(group.section);
            
            let bulkCategory;
            if (employeeCount <= 3) {
                bulkCategory = 'INDIVIDUAL';
            } else if (employeeCount <= 16) {
                bulkCategory = 'SMALL_BULK';
            } else if (employeeCount <= 32) {
                bulkCategory = 'MEDIUM_BULK';
            } else {
                bulkCategory = 'LARGE_BULK';
            }
            
            const categoryKey = `${bulkCategory}_${priority}`;
            
            if (categories[categoryKey]) {
                categories[categoryKey].changes.push(...group.changes);
                group.employees.forEach(emp => categories[categoryKey].employees.add(emp));
            }
        });
        
        // Display results
        console.log('\n📊 INDIVIDUAL ANOMALIES BREAKDOWN:');
        console.log('=' .repeat(60));
        
        const individualHighCount = categories['INDIVIDUAL_HIGH'].changes.length;
        const individualModerateCount = categories['INDIVIDUAL_MODERATE'].changes.length;
        const individualLowCount = categories['INDIVIDUAL_LOW'].changes.length;
        const totalIndividualCount = individualHighCount + individualModerateCount + individualLowCount;
        
        console.log(`🔴 HIGH Priority Individual Anomalies: ${individualHighCount.toLocaleString()} changes`);
        console.log(`   Affecting ${categories['INDIVIDUAL_HIGH'].employees.size} employees`);
        
        console.log(`🟡 MODERATE Priority Individual Anomalies: ${individualModerateCount.toLocaleString()} changes`);
        console.log(`   Affecting ${categories['INDIVIDUAL_MODERATE'].employees.size} employees`);
        
        console.log(`🟢 LOW Priority Individual Anomalies: ${individualLowCount.toLocaleString()} changes`);
        console.log(`   Affecting ${categories['INDIVIDUAL_LOW'].employees.size} employees`);
        
        console.log(`\n🎯 TOTAL INDIVIDUAL ANOMALIES: ${totalIndividualCount.toLocaleString()} changes`);
        console.log(`   Out of ${allResults.length.toLocaleString()} total changes`);
        console.log(`   Percentage: ${((totalIndividualCount / allResults.length) * 100).toFixed(1)}%`);
        
        // Show bulk breakdown for context
        console.log('\n📈 BULK CHANGES BREAKDOWN:');
        console.log('=' .repeat(60));
        
        const smallBulkTotal = categories['SMALL_BULK_HIGH'].changes.length + 
                              categories['SMALL_BULK_MODERATE'].changes.length + 
                              categories['SMALL_BULK_LOW'].changes.length;
        
        const mediumBulkTotal = categories['MEDIUM_BULK_HIGH'].changes.length + 
                               categories['MEDIUM_BULK_MODERATE'].changes.length + 
                               categories['MEDIUM_BULK_LOW'].changes.length;
        
        const largeBulkTotal = categories['LARGE_BULK_HIGH'].changes.length + 
                              categories['LARGE_BULK_MODERATE'].changes.length + 
                              categories['LARGE_BULK_LOW'].changes.length;
        
        console.log(`📦 Small Bulk Changes (4-16 employees): ${smallBulkTotal.toLocaleString()} changes`);
        console.log(`📦 Medium Bulk Changes (17-32 employees): ${mediumBulkTotal.toLocaleString()} changes`);
        console.log(`📦 Large Bulk Changes (33+ employees): ${largeBulkTotal.toLocaleString()} changes`);
        
        // Auto-selection summary
        console.log('\n🚀 AUTO-SELECTION SUMMARY:');
        console.log('=' .repeat(60));
        
        const autoSelectedIndividual = individualHighCount + individualModerateCount;
        const autoSelectedSmallBulkHigh = categories['SMALL_BULK_HIGH'].changes.length;
        const totalAutoSelected = autoSelectedIndividual + autoSelectedSmallBulkHigh;
        
        console.log(`✅ Auto-selected Individual Anomalies: ${autoSelectedIndividual.toLocaleString()} changes`);
        console.log(`✅ Auto-selected Small Bulk (HIGH): ${autoSelectedSmallBulkHigh.toLocaleString()} changes`);
        console.log(`🎯 TOTAL AUTO-SELECTED: ${totalAutoSelected.toLocaleString()} changes`);
        console.log(`   Percentage of total: ${((totalAutoSelected / allResults.length) * 100).toFixed(1)}%`);
        
        // Section breakdown for individual anomalies
        console.log('\n📋 INDIVIDUAL ANOMALIES BY SECTION:');
        console.log('=' .repeat(60));
        
        const sectionBreakdown = {};
        
        [...categories['INDIVIDUAL_HIGH'].changes, 
         ...categories['INDIVIDUAL_MODERATE'].changes, 
         ...categories['INDIVIDUAL_LOW'].changes].forEach(change => {
            if (!sectionBreakdown[change.section_name]) {
                sectionBreakdown[change.section_name] = {
                    count: 0,
                    priority: determinePriority(change.section_name)
                };
            }
            sectionBreakdown[change.section_name].count++;
        });
        
        // Sort by count descending
        const sortedSections = Object.entries(sectionBreakdown)
            .sort(([,a], [,b]) => b.count - a.count);
        
        sortedSections.forEach(([section, data]) => {
            const priorityIcon = data.priority === 'HIGH' ? '🔴' : 
                                data.priority === 'MODERATE' ? '🟡' : '🟢';
            console.log(`   ${priorityIcon} ${section}: ${data.count.toLocaleString()} changes`);
        });
        
        db.close();
        
        console.log('\n🎉 INDIVIDUAL ANOMALIES ANALYSIS COMPLETE!');
        console.log(`Your Interactive UI will auto-select ${totalAutoSelected.toLocaleString()} changes for review.`);
        
    } catch (error) {
        console.error('❌ Error counting individual anomalies:', error);
    }
}

// Run the analysis
if (require.main === module) {
    countIndividualAnomalies().then(() => {
        console.log('\n📊 Analysis complete!');
        process.exit(0);
    }).catch(error => {
        console.error('Analysis error:', error);
        process.exit(1);
    });
}
