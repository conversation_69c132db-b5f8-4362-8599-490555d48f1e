#!/usr/bin/env python3
"""Check the specific current session details"""

import sqlite3

def check_specific_session():
    """Check details of the current session"""
    print("🔍 CHECKING SPECIFIC SESSION DETAILS")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_result = cursor.fetchone()
        
        if not current_result:
            print("❌ No current session found")
            return
        
        current_session = current_result[0]
        print(f"📋 CURRENT SESSION: {current_session}")
        
        # Check if this session exists in audit_sessions table
        cursor.execute('SELECT * FROM audit_sessions WHERE session_id = ?', (current_session,))
        session_info = cursor.fetchone()
        
        if session_info:
            print(f"✅ Session found in audit_sessions table")
            print(f"   Created: {session_info[1]}")  # created_at
            print(f"   Status: {session_info[2]}")   # status
        else:
            print(f"❌ Session NOT found in audit_sessions table")
            print(f"   This explains why it didn't show up in recent sessions query")
        
        # Check data in each table for this session
        tables_to_check = [
            ('extracted_data', 'Extracted Data'),
            ('comparison_results', 'Comparison Results'), 
            ('pre_reporting_results', 'Pre-reporting Results')
        ]
        
        print(f"\n📊 DATA IN CURRENT SESSION:")
        total_data = 0
        
        for table_name, display_name in tables_to_check:
            cursor.execute(f'SELECT COUNT(*) FROM {table_name} WHERE session_id = ?', (current_session,))
            count = cursor.fetchone()[0]
            total_data += count
            
            status = "✅" if count > 0 else "❌"
            print(f"   {status} {display_name}: {count} records")
        
        # Check all sessions in audit_sessions table
        print(f"\n📋 ALL SESSIONS IN DATABASE:")
        cursor.execute('SELECT session_id, created_at, status FROM audit_sessions ORDER BY created_at DESC LIMIT 10')
        all_sessions = cursor.fetchall()
        
        if all_sessions:
            print(f"   Found {len(all_sessions)} sessions total:")
            for session_id, created_at, status in all_sessions:
                is_current = session_id == current_session
                marker = "👉 CURRENT" if is_current else "  "
                print(f"   {marker} {session_id} ({created_at}) - {status}")
        else:
            print(f"   ❌ No sessions found in audit_sessions table")
        
        # Summary and recommendations
        print(f"\n📋 ANALYSIS:")
        
        if session_info is None:
            print(f"🚨 PROBLEM: Current session pointer exists but session record is missing")
            print(f"   This suggests the audit process failed to create the session record")
            print(f"   Or the session was created but not properly recorded")
            
        if total_data == 0:
            print(f"🚨 PROBLEM: Current session has no data")
            print(f"   This suggests the audit process failed or was interrupted")
            
        if session_info and total_data > 0:
            print(f"✅ Session exists and has data - should work for UI")
        elif session_info and total_data == 0:
            print(f"⚠️ Session exists but has no data - audit may have failed")
        else:
            print(f"❌ Session missing or corrupted - need to run audit again")
        
        # Check if there are any sessions with data
        print(f"\n🔍 CHECKING FOR ANY SESSIONS WITH DATA:")
        cursor.execute('''
            SELECT DISTINCT session_id,
                   (SELECT COUNT(*) FROM extracted_data WHERE session_id = a.session_id) as extracted,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = a.session_id) as comparison,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = a.session_id) as pre_reporting
            FROM audit_sessions a
            WHERE EXISTS (
                SELECT 1 FROM extracted_data WHERE session_id = a.session_id
                UNION
                SELECT 1 FROM comparison_results WHERE session_id = a.session_id  
                UNION
                SELECT 1 FROM pre_reporting_results WHERE session_id = a.session_id
            )
            ORDER BY (
                (SELECT COUNT(*) FROM extracted_data WHERE session_id = a.session_id) +
                (SELECT COUNT(*) FROM comparison_results WHERE session_id = a.session_id) +
                (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = a.session_id)
            ) DESC
            LIMIT 5
        ''')
        
        sessions_with_data = cursor.fetchall()
        
        if sessions_with_data:
            print(f"   Found {len(sessions_with_data)} sessions with data:")
            for session_id, extracted, comparison, pre_reporting in sessions_with_data:
                total = extracted + comparison + pre_reporting
                print(f"   📊 {session_id}")
                print(f"      E={extracted}, C={comparison}, P={pre_reporting}, Total={total}")
                
                if pre_reporting > 0:
                    print(f"      ✅ HAS PRE-REPORTING DATA - Can be used for UI")
                    if session_id != current_session:
                        print(f"      🔧 Could switch to this session for UI")
        else:
            print(f"   ❌ No sessions with any data found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking session: {e}")

if __name__ == "__main__":
    check_specific_session()
