<!DOCTYPE html>
<html>
<head>
    <title>Browse Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .btn { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn.primary { background: #007bff; color: white; border: none; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🧪 Browse Button Functionality Test</h1>
    
    <div class="test-section">
        <h3>1. API Availability Test</h3>
        <div id="api-test-result" class="result">Testing...</div>
    </div>
    
    <div class="test-section">
        <h3>2. Button Element Test</h3>
        <button id="browse-current-payroll" class="btn primary">Browse Current Payroll</button>
        <button id="browse-previous-payroll" class="btn primary">Browse Previous Payroll</button>
        <div id="button-test-result" class="result">Click buttons above to test</div>
    </div>
    
    <div class="test-section">
        <h3>3. Function Test</h3>
        <button id="test-select-function" class="btn primary">Test selectAuditFile Function</button>
        <div id="function-test-result" class="result">Click button above to test</div>
    </div>

    <script>
        console.log('🧪 Starting browse button tests...');
        
        // Test 1: API Availability
        function testAPIAvailability() {
            const apiResult = document.getElementById('api-test-result');
            
            if (typeof window.api !== 'undefined') {
                if (typeof window.api.selectPdfFile === 'function') {
                    apiResult.innerHTML = '✅ window.api.selectPdfFile is available';
                    apiResult.style.background = '#d4edda';
                } else {
                    apiResult.innerHTML = '❌ window.api.selectPdfFile is not a function';
                    apiResult.style.background = '#f8d7da';
                }
            } else {
                apiResult.innerHTML = '❌ window.api is not available';
                apiResult.style.background = '#f8d7da';
            }
        }
        
        // Test 2: Button Elements
        function testButtonElements() {
            const currentBtn = document.getElementById('browse-current-payroll');
            const previousBtn = document.getElementById('browse-previous-payroll');
            const result = document.getElementById('button-test-result');
            
            if (currentBtn && previousBtn) {
                result.innerHTML = '✅ Both browse buttons found in DOM';
                result.style.background = '#d4edda';
                
                // Add test event listeners
                currentBtn.addEventListener('click', () => testFileSelection('current'));
                previousBtn.addEventListener('click', () => testFileSelection('previous'));
            } else {
                result.innerHTML = '❌ Browse buttons not found in DOM';
                result.style.background = '#f8d7da';
            }
        }
        
        // Test 3: File Selection Function
        async function testFileSelection(type) {
            const result = document.getElementById('button-test-result');
            
            try {
                result.innerHTML = `🔄 Testing ${type} file selection...`;
                result.style.background = '#fff3cd';
                
                if (typeof window.api.selectPdfFile === 'function') {
                    const filePath = await window.api.selectPdfFile();
                    
                    if (filePath) {
                        result.innerHTML = `✅ ${type} file selected: ${filePath}`;
                        result.style.background = '#d4edda';
                    } else {
                        result.innerHTML = `ℹ️ ${type} file selection canceled`;
                        result.style.background = '#d1ecf1';
                    }
                } else {
                    result.innerHTML = '❌ window.api.selectPdfFile is not available';
                    result.style.background = '#f8d7da';
                }
            } catch (error) {
                result.innerHTML = `❌ Error during ${type} file selection: ${error.message}`;
                result.style.background = '#f8d7da';
                console.error('File selection error:', error);
            }
        }
        
        // Test 4: Function Availability
        function testFunctionAvailability() {
            const testBtn = document.getElementById('test-select-function');
            const result = document.getElementById('function-test-result');
            
            testBtn.addEventListener('click', () => {
                if (typeof selectAuditFile === 'function') {
                    result.innerHTML = '✅ selectAuditFile function is available (local scope)';
                    result.style.background = '#d4edda';
                } else if (typeof window.selectAuditFile === 'function') {
                    result.innerHTML = '✅ window.selectAuditFile function is available (global scope)';
                    result.style.background = '#d4edda';
                } else {
                    result.innerHTML = '❌ selectAuditFile function is not available';
                    result.style.background = '#f8d7da';
                }
            });
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            testAPIAvailability();
            testButtonElements();
            testFunctionAvailability();
        });
        
        // Run tests immediately as well
        testAPIAvailability();
        testButtonElements();
        testFunctionAvailability();
    </script>
</body>
</html>
