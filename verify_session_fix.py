#!/usr/bin/env python3
"""Verify that the session fix prevents multiple session creation"""

import sqlite3

def verify_session_fix():
    """Verify that we now have proper session management"""
    print("🧪 VERIFYING SESSION FIX")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # 1. Check current session status
        print("1. 📋 CURRENT SESSION STATUS:")
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_result = cursor.fetchone()
        
        if current_result:
            current_session = current_result[0]
            print(f"   ✅ Current session: {current_session}")
            
            # Check data in current session
            cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
            extracted = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
            comparison = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (current_session,))
            pre_reporting = cursor.fetchone()[0]
            
            print(f"   📊 Data: Extracted={extracted}, Comparison={comparison}, Pre-reporting={pre_reporting}")
            
            if pre_reporting > 0:
                print("   ✅ Current session has pre-reporting data - UI should work!")
            else:
                print("   ❌ Current session has no pre-reporting data")
        else:
            print("   ❌ No current session found")
        
        # 2. Check for recent duplicate sessions
        print("\n2. 🔍 CHECKING FOR RECENT DUPLICATE SESSIONS:")
        cursor.execute('''
            SELECT session_id, created_at, status,
                   (SELECT COUNT(*) FROM extracted_data WHERE session_id = a.session_id) as extracted,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = a.session_id) as comparison,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = a.session_id) as pre_reporting
            FROM audit_sessions a
            WHERE created_at >= datetime('now', '-1 hour')
            ORDER BY created_at DESC
        ''')
        
        recent_sessions = cursor.fetchall()
        
        if recent_sessions:
            print(f"   📊 Found {len(recent_sessions)} sessions in the last hour:")
            for session in recent_sessions:
                session_id, created_at, status, extracted, comparison, pre_reporting = session
                print(f"      {session_id[:30]}... ({created_at})")
                print(f"        Status: {status}")
                print(f"        Data: E={extracted}, C={comparison}, P={pre_reporting}")
        else:
            print("   ✅ No recent sessions found")
        
        conn.close()
        
        # 3. Summary
        print("\n3. 📋 FIXES APPLIED:")
        print("   ✅ Removed duplicate startPayrollAuditProcess function from renderer.js")
        print("   ✅ Added session prevention mechanism")
        print("   ✅ Fixed session pointer to session with pre-reporting data")
        print("   ✅ Disabled duplicate event handlers")
        
        print("\n🎯 EXPECTED RESULTS:")
        print("   - Single button click should create only one session")
        print("   - UI should load pre-reporting interface successfully")
        print("   - No more SIGTERM errors from session conflicts")
        print("   - Pre-reporting data should be available for review")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

if __name__ == "__main__":
    verify_session_fix()
