// COMPREHENSIVE FILE UPLOAD DIAGNOSTIC TOOL
// Add this script to your HTML or run in browser console

console.log('🔍 STARTING COMPREHENSIVE FILE UPLOAD DIAGNOSTICS');
console.log('='.repeat(60));

// Global diagnostic object
window.fileUploadDiagnostics = {
    
    // Test 1: Check if elements exist
    checkElements() {
        console.log('\n📋 TEST 1: CHECKING DOM ELEMENTS');
        console.log('-'.repeat(40));
        
        const elements = {
            'browse-current-payroll': 'Current Browse Button',
            'browse-previous-payroll': 'Previous Browse Button', 
            'current-payroll-info': 'Current File Info',
            'previous-payroll-info': 'Previous File Info',
            'start-payroll-audit': 'Start Audit Button'
        };
        
        let allFound = true;
        
        Object.entries(elements).forEach(([id, name]) => {
            const element = document.getElementById(id);
            if (element) {
                console.log(`✅ ${name}: FOUND`);
                console.log(`   Element:`, element);
                console.log(`   Computed Style:`, window.getComputedStyle(element).display);
            } else {
                console.log(`❌ ${name}: NOT FOUND`);
                allFound = false;
            }
        });
        
        return allFound;
    },
    
    // Test 2: Check API availability
    checkAPI() {
        console.log('\n🔌 TEST 2: CHECKING API AVAILABILITY');
        console.log('-'.repeat(40));
        
        if (typeof window.api === 'undefined') {
            console.log('❌ window.api is undefined');
            return false;
        }
        
        if (typeof window.api.selectPdfFile !== 'function') {
            console.log('❌ window.api.selectPdfFile is not a function');
            return false;
        }
        
        console.log('✅ window.api.selectPdfFile is available');
        return true;
    },
    
    // Test 3: Check function availability
    checkFunctions() {
        console.log('\n⚙️ TEST 3: CHECKING FUNCTION AVAILABILITY');
        console.log('-'.repeat(40));
        
        const functions = {
            'selectAuditFile': 'File Selection Function',
            'handleAuditFileSelection': 'File Handler Function',
            'updateAuditFileInfo': 'File Info Update Function',
            'checkAuditButtonState': 'Button State Function'
        };
        
        let allFound = true;
        
        Object.entries(functions).forEach(([funcName, description]) => {
            if (typeof window[funcName] === 'function') {
                console.log(`✅ ${description}: AVAILABLE (window.${funcName})`);
            } else if (typeof eval(funcName) === 'function') {
                console.log(`✅ ${description}: AVAILABLE (local scope)`);
            } else {
                console.log(`❌ ${description}: NOT FOUND`);
                allFound = false;
            }
        });
        
        return allFound;
    },
    
    // Test 4: Check event listeners
    checkEventListeners() {
        console.log('\n👂 TEST 4: CHECKING EVENT LISTENERS');
        console.log('-'.repeat(40));

        const currentBtn = document.getElementById('browse-current-payroll');
        const previousBtn = document.getElementById('browse-previous-payroll');

        if (currentBtn) {
            console.log('✅ Current button found');
            // Check if getEventListeners is available (Chrome DevTools only)
            if (typeof getEventListeners === 'function') {
                try {
                    console.log('   Event listeners:', getEventListeners(currentBtn));
                } catch (error) {
                    console.log('   Event listeners: Cannot access (security restriction)');
                }
            } else {
                console.log('   Event listeners: Cannot check (getEventListeners not available - this is normal)');
            }
        }

        if (previousBtn) {
            console.log('✅ Previous button found');
            if (typeof getEventListeners === 'function') {
                try {
                    console.log('   Event listeners:', getEventListeners(previousBtn));
                } catch (error) {
                    console.log('   Event listeners: Cannot access (security restriction)');
                }
            } else {
                console.log('   Event listeners: Cannot check (getEventListeners not available - this is normal)');
            }
        }
    },
    
    // Test 5: Simulate file selection
    async simulateFileSelection(type) {
        console.log(`\n🎭 TEST 5: SIMULATING ${type.toUpperCase()} FILE SELECTION`);
        console.log('-'.repeat(40));
        
        try {
            // Check if API is available
            if (typeof window.api?.selectPdfFile !== 'function') {
                console.log('❌ API not available for simulation');
                return false;
            }
            
            console.log('🔄 Calling window.api.selectPdfFile()...');
            const filePath = await window.api.selectPdfFile();
            
            if (filePath) {
                console.log(`✅ File selected: ${filePath}`);
                
                // Test handleAuditFileSelection
                if (typeof handleAuditFileSelection === 'function') {
                    console.log('🔄 Calling handleAuditFileSelection...');
                    handleAuditFileSelection(filePath, type);
                    console.log('✅ handleAuditFileSelection completed');
                } else if (typeof window.handleAuditFileSelection === 'function') {
                    console.log('🔄 Calling window.handleAuditFileSelection...');
                    window.handleAuditFileSelection(filePath, type);
                    console.log('✅ window.handleAuditFileSelection completed');
                } else {
                    console.log('❌ handleAuditFileSelection function not found');
                    return false;
                }
                
                return true;
            } else {
                console.log('ℹ️ No file selected (user canceled)');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Error during simulation: ${error.message}`);
            return false;
        }
    },
    
    // Test 6: Check variables
    checkVariables() {
        console.log('\n📊 TEST 6: CHECKING VARIABLES');
        console.log('-'.repeat(40));
        
        console.log('Local variables:');
        console.log(`  auditCurrentPdfPath: ${typeof auditCurrentPdfPath !== 'undefined' ? auditCurrentPdfPath : 'undefined'}`);
        console.log(`  auditPreviousPdfPath: ${typeof auditPreviousPdfPath !== 'undefined' ? auditPreviousPdfPath : 'undefined'}`);
        
        console.log('Window variables:');
        console.log(`  window.auditCurrentPdfPath: ${window.auditCurrentPdfPath || 'undefined'}`);
        console.log(`  window.auditPreviousPdfPath: ${window.auditPreviousPdfPath || 'undefined'}`);
    },
    
    // Test 7: Manual file info update
    testFileInfoUpdate() {
        console.log('\n🔧 TEST 7: MANUAL FILE INFO UPDATE');
        console.log('-'.repeat(40));
        
        const testPath = 'C:\\Test\\sample-payroll.pdf';
        
        // Test current file info
        const currentInfo = document.getElementById('current-payroll-info');
        if (currentInfo) {
            console.log('🔄 Testing current file info update...');
            const fileName = testPath.split('\\').pop();
            currentInfo.innerHTML = `<i class="fas fa-check-circle"></i> ${fileName}`;
            currentInfo.style.display = 'block';
            currentInfo.style.visibility = 'visible';
            currentInfo.style.opacity = '1';
            
            const computedStyle = window.getComputedStyle(currentInfo);
            console.log(`✅ Current file info updated`);
            console.log(`   Display: ${computedStyle.display}`);
            console.log(`   Visibility: ${computedStyle.visibility}`);
            console.log(`   Opacity: ${computedStyle.opacity}`);
        }
        
        // Test previous file info
        const previousInfo = document.getElementById('previous-payroll-info');
        if (previousInfo) {
            console.log('🔄 Testing previous file info update...');
            const fileName = testPath.split('\\').pop();
            previousInfo.innerHTML = `<i class="fas fa-check-circle"></i> ${fileName}`;
            previousInfo.style.display = 'block';
            previousInfo.style.visibility = 'visible';
            previousInfo.style.opacity = '1';
            
            const computedStyle = window.getComputedStyle(previousInfo);
            console.log(`✅ Previous file info updated`);
            console.log(`   Display: ${computedStyle.display}`);
            console.log(`   Visibility: ${computedStyle.visibility}`);
            console.log(`   Opacity: ${computedStyle.opacity}`);
        }
    },
    
    // Test 8: Manual button state update
    testButtonState() {
        console.log('\n🔘 TEST 8: MANUAL BUTTON STATE UPDATE');
        console.log('-'.repeat(40));
        
        const startBtn = document.getElementById('start-payroll-audit');
        if (startBtn) {
            console.log('🔄 Testing button state update...');
            
            // Force enable button
            startBtn.disabled = false;
            startBtn.style.opacity = '1';
            startBtn.style.cursor = 'pointer';
            startBtn.style.backgroundColor = '#28a745';
            startBtn.style.color = 'white';
            startBtn.style.boxShadow = '0 4px 12px rgba(40, 167, 69, 0.3)';
            
            console.log('✅ Button manually enabled');
            console.log(`   Disabled: ${startBtn.disabled}`);
            console.log(`   Background: ${startBtn.style.backgroundColor}`);
            console.log(`   Opacity: ${startBtn.style.opacity}`);
        }
    },
    
    // Run all tests
    runAllTests() {
        console.log('🚀 RUNNING ALL DIAGNOSTIC TESTS');
        console.log('='.repeat(60));
        
        const results = {
            elements: this.checkElements(),
            api: this.checkAPI(),
            functions: this.checkFunctions(),
            variables: this.checkVariables()
        };
        
        // Optional tests (non-critical)
        try {
            this.checkEventListeners();
        } catch (error) {
            console.log('⚠️ Event listener check skipped:', error.message);
        }

        this.testFileInfoUpdate();
        this.testButtonState();
        
        console.log('\n📋 DIAGNOSTIC SUMMARY');
        console.log('='.repeat(60));
        console.log(`Elements: ${results.elements ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`API: ${results.api ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`Functions: ${results.functions ? '✅ PASS' : '❌ FAIL'}`);
        
        if (results.elements && results.api && results.functions) {
            console.log('\n🎉 ALL CORE TESTS PASSED - File upload should work!');
            console.log('💡 Try the manual tests above to see if UI updates work');
        } else {
            console.log('\n❌ SOME TESTS FAILED - Issues need to be fixed');
        }
        
        return results;
    }
};

// Auto-run diagnostics
window.fileUploadDiagnostics.runAllTests();

console.log('\n💡 MANUAL TESTING COMMANDS:');
console.log('- window.fileUploadDiagnostics.simulateFileSelection("current")');
console.log('- window.fileUploadDiagnostics.simulateFileSelection("previous")');
console.log('- window.fileUploadDiagnostics.testFileInfoUpdate()');
console.log('- window.fileUploadDiagnostics.testButtonState()');
