#!/usr/bin/env python3
"""Debug why no external loans were detected"""

import sqlite3

def debug_external_loans():
    """Debug external loan detection and classification"""
    print("🔍 DEBUGGING EXTERNAL LOAN DETECTION")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check all loan-related items in comparison results
        print(f"\n🔍 ALL LOAN-RELATED ITEMS IN COMPARISON RESULTS:")
        
        cursor.execute('''
            SELECT DISTINCT section_name, item_label, change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ? AND (
                section_name LIKE '%LOAN%' OR
                item_label LIKE '%LOAN%' OR
                item_label LIKE '%ADVANCE%' OR
                item_label LIKE '%BANK%' OR
                item_label LIKE '%GCB%' OR
                item_label LIKE '%ECOBANK%' OR
                item_label LIKE '%ADB%'
            )
            GROUP BY section_name, item_label, change_type
            ORDER BY section_name, item_label
        ''', (session_id,))
        
        all_loans = cursor.fetchall()
        if all_loans:
            print("   All loan-related items found:")
            for row in all_loans:
                print(f"     {row[0]}.{row[1]} ({row[2]}) - {row[3]} records")
        else:
            print("   ❌ No loan-related items found")
        
        # Check specifically for external bank loans
        print(f"\n🔍 POTENTIAL EXTERNAL BANK LOANS:")
        
        external_patterns = [
            'GCB BANK',
            'ECOBANK', 
            'ADB LOANS',
            'OMNI BANK',
            'ABII NATIONAL',
            'PENCO DANSOMAN',
            'GLICO LIFE INSURANCE',
            'GHANA LIFE INS'
        ]
        
        for pattern in external_patterns:
            cursor.execute('''
                SELECT DISTINCT item_label, change_type, COUNT(*) as count
                FROM comparison_results 
                WHERE session_id = ? AND item_label LIKE ?
                GROUP BY item_label, change_type
                ORDER BY item_label
            ''', (session_id, f'%{pattern}%'))
            
            pattern_results = cursor.fetchall()
            if pattern_results:
                print(f"   {pattern} loans:")
                for row in pattern_results:
                    print(f"     {row[0]} ({row[1]}) - {row[2]} records")
            else:
                print(f"   {pattern}: No loans found")
        
        # Check what NEW external loan items exist
        print(f"\n🔍 NEW EXTERNAL LOAN ITEMS:")
        
        cursor.execute('''
            SELECT DISTINCT item_label, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW' AND (
                item_label LIKE '%GCB%' OR
                item_label LIKE '%ECOBANK%' OR
                item_label LIKE '%ADB%' OR
                item_label LIKE '%OMNI%' OR
                item_label LIKE '%ABII%' OR
                item_label LIKE '%PENCO%' OR
                item_label LIKE '%GLICO%' OR
                item_label LIKE '%GHANA LIFE%'
            )
            GROUP BY item_label
            ORDER BY count DESC
        ''', (session_id,))
        
        new_external = cursor.fetchall()
        if new_external:
            print("   NEW external loan items:")
            for row in new_external:
                print(f"     {row[0]} - {row[1]} records")
        else:
            print("   ❌ No NEW external loan items found")
        
        # Check in-house loan dictionary to see what's classified as in-house
        print(f"\n🔍 IN-HOUSE LOAN DICTIONARY:")
        
        cursor.execute('SELECT loan_type FROM in_house_loan_types ORDER BY loan_type')
        in_house_types = cursor.fetchall()
        
        if in_house_types:
            print("   In-house loan types:")
            for row in in_house_types:
                print(f"     {row[0]}")
        else:
            print("   ❌ No in-house loan types found")
        
        # Check what items were actually tracked
        print(f"\n🔍 WHAT WAS ACTUALLY TRACKED:")
        
        cursor.execute('''
            SELECT tracker_type, item_label, COUNT(*) as count
            FROM tracker_results 
            WHERE session_id = ? AND tracker_type IN ('IN_HOUSE_LOAN', 'EXTERNAL_LOAN')
            GROUP BY tracker_type, item_label
            ORDER BY tracker_type, count DESC
        ''', (session_id,))
        
        tracked_loans = cursor.fetchall()
        if tracked_loans:
            print("   Tracked loan items:")
            for row in tracked_loans:
                print(f"     {row[0]}: {row[1]} - {row[2]} records")
        else:
            print("   ❌ No loan items were tracked")
        
        conn.close()
        
        print(f"\n💡 ANALYSIS:")
        print("1. Check if external bank loans exist in comparison_results")
        print("2. Verify if they are marked as NEW items")
        print("3. Check if classification logic correctly identifies external loans")
        print("4. Ensure external loans are not incorrectly classified as in-house")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_external_loans()
