#!/usr/bin/env python3
"""Fix session pointer to point to session with pre-reporting data"""

import sqlite3

def fix_session_pointer():
    """Point current session to the one with pre-reporting data"""
    print("🔧 FIXING SESSION POINTER")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # The session with pre-reporting data
        good_session = 'audit_session_1751218345_cb7183d3'
        
        print(f"📋 Switching to session: {good_session}")
        
        # Update current session
        cursor.execute('''
            UPDATE current_session 
            SET session_id = ?, updated_at = datetime('now') 
            WHERE id = 1
        ''', (good_session,))
        
        # Update session status to ready for UI
        cursor.execute('''
            UPDATE audit_sessions 
            SET status = 'pre_reporting_ready' 
            WHERE session_id = ?
        ''', (good_session,))
        
        conn.commit()
        
        # Verify the fix
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (current,))
        pre_reporting_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ Current session: {current}")
        print(f"✅ Pre-reporting data: {pre_reporting_count} records")
        print("✅ Session status: pre_reporting_ready")
        print("\n🎉 SUCCESS: UI should now work perfectly!")
        print("💡 Try refreshing the app and the pre-reporting interface should load")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    fix_session_pointer()
