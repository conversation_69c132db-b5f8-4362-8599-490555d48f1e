#!/usr/bin/env python3
"""Debug SIGTERM issue with interactive UI"""

import sqlite3

def debug_sigterm_issue():
    """Debug why interactive UI failed with SIGTERM"""
    print("🔍 DEBUGGING SIGTERM ISSUE")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check session phase status
        print(f"\n🔍 SESSION PHASE STATUS:")
        
        cursor.execute('''
            SELECT phase_name, status, data_count, started_at, completed_at
            FROM session_phases 
            WHERE session_id = ?
            ORDER BY 
                CASE phase_name
                    WHEN 'EXTRACTION' THEN 1
                    WHEN 'COMPARISON' THEN 2
                    WHEN 'AUTO_LEARNING' THEN 3
                    WHEN 'TRACKER_FEEDING' THEN 4
                    WHEN 'PRE_REPORTING' THEN 5
                    ELSE 6
                END
        ''', (session_id,))
        
        phases = cursor.fetchall()
        if phases:
            for phase in phases:
                phase_name, status, data_count, started, completed = phase
                print(f"   {phase_name}: {status} ({data_count} records)")
                if status == 'WAITING_FOR_USER':
                    print(f"     ✅ CORRECTLY waiting for user interaction")
                elif status == 'COMPLETED' and phase_name == 'PRE_REPORTING':
                    print(f"     ❌ PROBLEM: PRE_REPORTING auto-completed (should be WAITING_FOR_USER)")
        else:
            print("   ❌ No phase records found")
        
        # Check pre-reporting data availability
        print(f"\n🔍 PRE-REPORTING DATA STATUS:")
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session_id,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   Pre-reporting records: {pre_reporting_count}")
        
        if pre_reporting_count > 0:
            # Check data structure
            cursor.execute('''
                SELECT category, COUNT(*) as count
                FROM pre_reporting_results 
                WHERE session_id = ?
                GROUP BY category
                ORDER BY count DESC
                LIMIT 10
            ''', (session_id,))
            
            categories = cursor.fetchall()
            print("   Categories breakdown:")
            for cat, count in categories:
                print(f"     {cat}: {count} records")
        else:
            print("   ❌ No pre-reporting data available")
        
        # Check if there are any running processes
        print(f"\n🔍 POTENTIAL SIGTERM CAUSES:")
        
        # Check for database locks
        cursor.execute('PRAGMA database_list')
        db_info = cursor.fetchall()
        print(f"   Database status: {len(db_info)} databases connected")
        
        # Check session status
        cursor.execute('SELECT status FROM audit_sessions WHERE session_id = ?', (session_id,))
        session_status = cursor.fetchone()
        if session_status:
            print(f"   Session status: {session_status[0]}")
            
            if session_status[0] == 'pre_reporting_ready':
                print("     ✅ Session ready for interactive UI")
            elif session_status[0] == 'processing':
                print("     ❌ PROBLEM: Session still processing (may cause SIGTERM)")
            else:
                print(f"     ⚠️ Unexpected status: {session_status[0]}")
        else:
            print("   ❌ No session status found")
        
        # Check for any incomplete operations
        print(f"\n🔍 WORKFLOW PAUSE FIX STATUS:")
        
        # The fix should prevent auto-completion of PRE_REPORTING
        pre_reporting_phase = None
        for phase in phases:
            if phase[0] == 'PRE_REPORTING':
                pre_reporting_phase = phase
                break
        
        if pre_reporting_phase:
            phase_name, status, data_count, started, completed = pre_reporting_phase
            
            if status == 'WAITING_FOR_USER':
                print("   ✅ Workflow pause fix WORKING - PRE_REPORTING waiting for user")
                print("   💡 Interactive UI should load without SIGTERM")
            elif status == 'COMPLETED':
                print("   ❌ Workflow pause fix FAILED - PRE_REPORTING auto-completed")
                print("   💡 This may cause SIGTERM when UI tries to load")
                print("   💡 Need to manually set status to WAITING_FOR_USER")
            else:
                print(f"   ⚠️ Unexpected PRE_REPORTING status: {status}")
        
        conn.close()
        
        print(f"\n💡 SIGTERM ANALYSIS:")
        print("1. SIGTERM usually occurs when:")
        print("   - Process is still running when UI tries to load")
        print("   - Database is locked by background process")
        print("   - PRE_REPORTING phase auto-completed instead of waiting")
        print("2. Check if workflow pause fix is working")
        print("3. Verify session status is 'pre_reporting_ready'")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_sigterm_issue()
