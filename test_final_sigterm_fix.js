#!/usr/bin/env node
/**
 * Test Final SIGTERM Fix
 * Verify that all SIGTERM and multiple tracker population issues are resolved
 */

const fs = require('fs');
const path = require('path');

async function testFinalSIGTERMFix() {
    console.log('🧪 TESTING FINAL SIGTERM FIX');
    console.log('=' .repeat(60));
    
    let score = 0;
    const maxScore = 8;
    
    try {
        // Test 1: Check tracker population coordination
        console.log('\n1. 🔍 TESTING TRACKER POPULATION COORDINATION:');
        
        const rendererPath = path.join(__dirname, 'renderer.js');
        if (fs.existsSync(rendererPath)) {
            const content = fs.readFileSync(rendererPath, 'utf8');
            
            // Check that activateRedesignedPreReporting doesn't call tracker population
            const activationFunction = content.substring(
                content.indexOf('async function activateRedesignedPreReporting'),
                content.indexOf('} finally {', content.indexOf('async function activateRedesignedPreReporting')) + 10
            );
            
            const hasTrackerCall = activationFunction.includes('populateTrackerTablesRedesigned()');
            const hasSkipMessage = activationFunction.includes('Skipping tracker population');
            
            console.log(`   Activation function calls tracker: ${hasTrackerCall ? '❌' : '✅'}`);
            console.log(`   Has skip message: ${hasSkipMessage ? '✅' : '❌'}`);
            
            if (!hasTrackerCall && hasSkipMessage) {
                console.log('   ✅ Tracker population properly coordinated');
                score++;
            } else {
                console.log('   ❌ Tracker population coordination issues');
            }
        }
        
        // Test 2: Check UI coordinator tracker integration
        console.log('\n2. 🔍 TESTING UI COORDINATOR TRACKER INTEGRATION:');
        
        const coordinatorPath = path.join(__dirname, 'core', 'ui_loading_coordinator.js');
        if (fs.existsSync(coordinatorPath)) {
            const content = fs.readFileSync(coordinatorPath, 'utf8');
            
            const hasTrackerStep = content.includes('Step 1: Safe tracker population');
            const hasConflictCheck = content.includes('!window.isPopulatingTracker');
            const hasContinueOnError = content.includes('Continue with UI loading even if tracker population fails');
            
            console.log(`   Has tracker step: ${hasTrackerStep ? '✅' : '❌'}`);
            console.log(`   Has conflict check: ${hasConflictCheck ? '✅' : '❌'}`);
            console.log(`   Continues on error: ${hasContinueOnError ? '✅' : '❌'}`);
            
            if (hasTrackerStep && hasConflictCheck && hasContinueOnError) {
                console.log('   ✅ UI coordinator properly integrated');
                score++;
            } else {
                console.log('   ❌ UI coordinator integration issues');
            }
        }
        
        // Test 3: Check payroll audit conflict prevention
        console.log('\n3. 🔍 TESTING PAYROLL AUDIT CONFLICT PREVENTION:');
        
        const auditCorePath = path.join(__dirname, 'ui', 'payroll_audit_core.js');
        if (fs.existsSync(auditCorePath)) {
            const content = fs.readFileSync(auditCorePath, 'utf8');
            
            const hasConflictCheck = content.includes('isActivatingPreReporting || window.isPopulatingTracker');
            const hasPreventionMessage = content.includes('Interactive UI is active, cannot start new audit');
            const hasAlert = content.includes('Please wait for it to complete');
            
            console.log(`   Has conflict check: ${hasConflictCheck ? '✅' : '❌'}`);
            console.log(`   Has prevention message: ${hasPreventionMessage ? '✅' : '❌'}`);
            console.log(`   Has user alert: ${hasAlert ? '✅' : '❌'}`);
            
            if (hasConflictCheck && hasPreventionMessage && hasAlert) {
                console.log('   ✅ Payroll audit conflict prevention working');
                score++;
            } else {
                console.log('   ❌ Payroll audit conflict prevention issues');
            }
        }
        
        // Test 4: Check safe tracker population implementation
        console.log('\n4. 🔍 TESTING SAFE TRACKER POPULATION:');
        
        const safeTrackerPath = path.join(__dirname, 'core', 'safe_tracker_population.js');
        if (fs.existsSync(safeTrackerPath)) {
            const content = fs.readFileSync(safeTrackerPath, 'utf8');
            
            const hasLockManager = content.includes('getDatabaseLockManager');
            const hasPopulationLock = content.includes('this.isPopulating');
            const hasColumnMapping = content.includes('employee_no') && content.includes('department');
            const noPythonSpawn = !content.includes('spawn') && !content.includes('python');
            
            console.log(`   Uses lock manager: ${hasLockManager ? '✅' : '❌'}`);
            console.log(`   Has population lock: ${hasPopulationLock ? '✅' : '❌'}`);
            console.log(`   Has column mapping: ${hasColumnMapping ? '✅' : '❌'}`);
            console.log(`   No Python spawning: ${noPythonSpawn ? '✅' : '❌'}`);
            
            if (hasLockManager && hasPopulationLock && hasColumnMapping && noPythonSpawn) {
                console.log('   ✅ Safe tracker population properly implemented');
                score++;
            } else {
                console.log('   ❌ Safe tracker population has issues');
            }
        }
        
        // Test 5: Check activation locks
        console.log('\n5. 🔍 TESTING ACTIVATION LOCKS:');
        
        if (fs.existsSync(rendererPath)) {
            const content = fs.readFileSync(rendererPath, 'utf8');
            
            const hasActivationLock = content.includes('isActivatingPreReporting = false');
            const hasTrackerLock = content.includes('isPopulatingTracker = false');
            const hasLockReset = content.includes('finally {') && content.includes('lock released');
            const hasMinInterval = content.includes('MIN_ACTIVATION_INTERVAL');
            
            console.log(`   Has activation lock: ${hasActivationLock ? '✅' : '❌'}`);
            console.log(`   Has tracker lock: ${hasTrackerLock ? '✅' : '❌'}`);
            console.log(`   Has lock reset: ${hasLockReset ? '✅' : '❌'}`);
            console.log(`   Has min interval: ${hasMinInterval ? '✅' : '❌'}`);
            
            if (hasActivationLock && hasTrackerLock && hasLockReset && hasMinInterval) {
                console.log('   ✅ Activation locks properly implemented');
                score++;
            } else {
                console.log('   ❌ Activation locks missing or incomplete');
            }
        }
        
        // Test 6: Check IPC handler safety
        console.log('\n6. 🔍 TESTING IPC HANDLER SAFETY:');
        
        const mainPath = path.join(__dirname, 'main.js');
        if (fs.existsSync(mainPath)) {
            const content = fs.readFileSync(mainPath, 'utf8');
            
            const hasSafeTracker = content.includes('SAFE: Populate tracker tables using database lock manager');
            const noDirectPython = !content.includes('populate-tracker-tables') || content.includes('NO PYTHON PROCESSES');
            
            console.log(`   Has safe tracker handler: ${hasSafeTracker ? '✅' : '❌'}`);
            console.log(`   No direct Python spawning: ${noDirectPython ? '✅' : '❌'}`);
            
            if (hasSafeTracker && noDirectPython) {
                console.log('   ✅ IPC handlers are safe');
                score++;
            } else {
                console.log('   ❌ IPC handlers may cause issues');
            }
        }
        
        // Test 7: Check Interactive UI connection
        console.log('\n7. 🔍 TESTING INTERACTIVE UI CONNECTION:');
        
        const interactiveUIPath = path.join(__dirname, 'ui', 'interactive_pre_reporting.js');
        if (fs.existsSync(interactiveUIPath)) {
            const content = fs.readFileSync(interactiveUIPath, 'utf8');
            
            const hasRenderMethods = content.includes('processAndRender') && content.includes('render(');
            const hasDataLoading = content.includes('loadDataFromDatabase');
            const hasErrorHandling = content.includes('showError');
            const hasChunkedRendering = content.includes('renderChunked');
            
            console.log(`   Has render methods: ${hasRenderMethods ? '✅' : '❌'}`);
            console.log(`   Has data loading: ${hasDataLoading ? '✅' : '❌'}`);
            console.log(`   Has error handling: ${hasErrorHandling ? '✅' : '❌'}`);
            console.log(`   Has chunked rendering: ${hasChunkedRendering ? '✅' : '❌'}`);
            
            if (hasRenderMethods && hasDataLoading && hasErrorHandling && hasChunkedRendering) {
                console.log('   ✅ Interactive UI properly connected');
                score++;
            } else {
                console.log('   ❌ Interactive UI connection issues');
            }
        }
        
        // Test 8: Check database state
        console.log('\n8. 🔍 TESTING DATABASE STATE:');
        
        try {
            const sqlite3 = require('sqlite3').verbose();
            const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
            
            if (fs.existsSync(dbPath)) {
                console.log('   ✅ Database file exists');
                score++;
            } else {
                console.log('   ❌ Database file missing');
            }
        } catch (error) {
            console.log(`   ⚠️ Database test error: ${error.message}`);
        }
        
        // Final results
        console.log('\n🎯 FINAL SIGTERM FIX TEST RESULTS:');
        console.log('=' .repeat(60));
        console.log(`Score: ${score}/${maxScore}`);
        
        if (score >= 7) {
            console.log('🎉 SIGTERM AND TRACKER ISSUES RESOLVED!');
            console.log('✅ Tracker population coordination fixed');
            console.log('✅ UI coordinator integration working');
            console.log('✅ Payroll audit conflict prevention active');
            console.log('✅ Safe tracker population implemented');
            console.log('✅ Activation locks working');
            console.log('✅ IPC handlers are safe');
            console.log('✅ Interactive UI properly connected');
            console.log('✅ Database state is good');
            
            console.log('\n🚀 READY FOR PRODUCTION TESTING:');
            console.log('1. Start the Electron app');
            console.log('2. Interactive UI should load without SIGTERM');
            console.log('3. Tracker tables should populate once, safely');
            console.log('4. No "Population already in progress" errors');
            console.log('5. No conflicts between audit and UI loading');
            
            return true;
        } else {
            console.log('⚠️ SOME ISSUES REMAIN');
            console.log('Additional fixes may be needed');
            
            console.log('\n🔧 TROUBLESHOOTING:');
            if (score < 2) console.log('- Fix tracker population coordination');
            if (score < 3) console.log('- Fix UI coordinator integration');
            if (score < 4) console.log('- Fix payroll audit conflict prevention');
            if (score < 5) console.log('- Fix safe tracker population');
            if (score < 6) console.log('- Fix activation locks');
            if (score < 7) console.log('- Fix IPC handler safety');
            if (score < 8) console.log('- Fix Interactive UI connection');
            
            return false;
        }
        
    } catch (error) {
        console.error('❌ Test error:', error);
        return false;
    }
}

// Run the test
if (require.main === module) {
    testFinalSIGTERMFix().then(success => {
        if (success) {
            console.log('\n🎯 ALL SYSTEMS GO!');
            console.log('The SIGTERM and tracker population issues should be resolved.');
            console.log('Test the Electron app now!');
        } else {
            console.log('\n⚠️ ADDITIONAL WORK NEEDED:');
            console.log('Review the test results and apply remaining fixes.');
        }
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('Test execution error:', error);
        process.exit(1);
    });
}
