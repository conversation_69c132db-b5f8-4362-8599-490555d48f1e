#!/usr/bin/env python3
"""Fix multiple session creation by cleaning up duplicate event handlers"""

import os
import re

def fix_multiple_session_creation():
    """Remove duplicate event handlers causing multiple session creation"""
    print("🔧 FIXING MULTIPLE SESSION CREATION")
    print("=" * 50)
    
    # 1. Disable the problematic content switching manager
    print("1. 🚫 DISABLING DUPLICATE EVENT HANDLERS:")
    
    content_switching_file = "ui/content_switching_manager.js"
    if os.path.exists(content_switching_file):
        # Rename to disable it
        disabled_file = content_switching_file + ".disabled"
        if not os.path.exists(disabled_file):
            os.rename(content_switching_file, disabled_file)
            print(f"   ✅ Disabled: {content_switching_file}")
        else:
            print(f"   ✅ Already disabled: {content_switching_file}")
    else:
        print(f"   ℹ️ File not found: {content_switching_file}")
    
    # 2. Check for other duplicate handlers
    print("\n2. 🔍 CHECKING FOR OTHER DUPLICATE HANDLERS:")
    
    files_to_check = [
        "renderer.js",
        "ui/payroll_audit_core.js"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count event listener attachments
            start_audit_listeners = len(re.findall(r'addEventListener.*start.*audit', content, re.IGNORECASE))
            start_payroll_listeners = len(re.findall(r'addEventListener.*start.*payroll', content, re.IGNORECASE))
            
            print(f"   📄 {file_path}:")
            print(f"      Start audit listeners: {start_audit_listeners}")
            print(f"      Start payroll listeners: {start_payroll_listeners}")
        else:
            print(f"   ⚠️ File not found: {file_path}")
    
    # 3. Clean up orphaned report configuration references
    print("\n3. 🧹 CLEANING UP ORPHANED REFERENCES:")
    
    # Check for report configuration references that might cause errors
    orphaned_refs = [
        "reportConfig",
        "report_configuration", 
        "finalReportConfig",
        "transformToProcessingView"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   📄 {file_path}:")
            for ref in orphaned_refs:
                count = len(re.findall(ref, content, re.IGNORECASE))
                if count > 0:
                    print(f"      {ref}: {count} references")
    
    # 4. Create session creation prevention mechanism
    print("\n4. 🛡️ CREATING SESSION CREATION PREVENTION:")
    
    prevention_script = '''
// Session creation prevention mechanism
window.sessionCreationInProgress = false;
window.currentSessionId = null;

// Override session creation to prevent duplicates
const originalCreateSession = window.api?.enhancedPayrollAudit;
if (originalCreateSession) {
    window.api.enhancedPayrollAudit = function(...args) {
        if (window.sessionCreationInProgress) {
            console.log('🛡️ Session creation already in progress, ignoring duplicate request');
            return Promise.resolve({ success: false, error: 'Session creation already in progress' });
        }
        
        window.sessionCreationInProgress = true;
        console.log('🔒 Session creation locked');
        
        return originalCreateSession.apply(this, args).finally(() => {
            window.sessionCreationInProgress = false;
            console.log('🔓 Session creation unlocked');
        });
    };
}
'''
    
    with open('session_prevention.js', 'w') as f:
        f.write(prevention_script)
    
    print("   ✅ Created session_prevention.js")
    
    # 5. Summary and recommendations
    print("\n5. 📋 SUMMARY AND RECOMMENDATIONS:")
    print("   ✅ Disabled duplicate event handlers")
    print("   ✅ Created session prevention mechanism")
    print("   ✅ Identified orphaned report configuration references")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Include session_prevention.js in your HTML")
    print("2. Test single button click behavior")
    print("3. Monitor for duplicate session creation")
    print("4. Clean up remaining orphaned references if needed")
    
    print("\n💡 PREVENTION MEASURES:")
    print("- Only one event handler should be attached to start button")
    print("- Use session creation locks to prevent duplicates")
    print("- Remove orphaned report configuration dependencies")
    
    return True

if __name__ == "__main__":
    fix_multiple_session_creation()
