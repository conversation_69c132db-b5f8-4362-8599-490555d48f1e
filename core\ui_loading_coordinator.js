/**
 * UI Loading Coordinator
 * Prevents SIGTERM by coordinating all UI loading attempts into a single, managed process
 */

class UILoadingCoordinator {
    constructor() {
        this.isLoading = false;
        this.loadingPromise = null;
        this.loadingQueue = [];
        this.lastLoadTime = 0;
        this.minLoadInterval = 5000; // 5 seconds minimum between loads
        this.maxRetries = 2;
    }

    async loadPreReportingUI(source = 'unknown', retryCount = 0) {
        const requestId = `${source}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        console.log(`[UI-COORDINATOR] Load request from ${source} (ID: ${requestId})`);

        // Check if we're already loading
        if (this.isLoading) {
            console.log(`[UI-COORDINATOR] Already loading, queuing request from ${source}`);
            return this.waitForCurrentLoad();
        }

        // Check minimum interval
        const timeSinceLastLoad = Date.now() - this.lastLoadTime;
        if (timeSinceLastLoad < this.minLoadInterval) {
            const waitTime = this.minLoadInterval - timeSinceLastLoad;
            console.log(`[UI-COORDINATOR] Rate limiting: waiting ${waitTime}ms before load from ${source}`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        // Start loading
        this.isLoading = true;
        this.lastLoadTime = Date.now();
        
        console.log(`[UI-COORDINATOR] Starting UI load from ${source}...`);

        try {
            // Create loading promise
            this.loadingPromise = this.executeUILoad(source, retryCount);
            const result = await this.loadingPromise;
            
            console.log(`[UI-COORDINATOR] UI load completed successfully from ${source}`);
            return result;

        } catch (error) {
            console.error(`[UI-COORDINATOR] UI load failed from ${source}:`, error);
            
            // Retry logic
            if (retryCount < this.maxRetries && this.shouldRetry(error)) {
                console.log(`[UI-COORDINATOR] Retrying load from ${source} (${retryCount + 1}/${this.maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, 2000)); // Wait before retry
                return this.loadPreReportingUI(source, retryCount + 1);
            }
            
            throw error;

        } finally {
            this.isLoading = false;
            this.loadingPromise = null;
            
            // Process any queued requests
            this.processQueue();
        }
    }

    async waitForCurrentLoad() {
        if (this.loadingPromise) {
            console.log('[UI-COORDINATOR] Waiting for current load to complete...');
            try {
                return await this.loadingPromise;
            } catch (error) {
                console.log('[UI-COORDINATOR] Current load failed, will not retry from queue');
                throw error;
            }
        }
        return null;
    }

    async executeUILoad(source, retryCount) {
        console.log(`[UI-COORDINATOR] Executing UI load from ${source}...`);

        // Check if API is available
        if (!window.api || !window.api.getLatestPreReportingData) {
            throw new Error('Pre-reporting API not available');
        }

        // Get the data using our enhanced column mapping
        const response = await window.api.getLatestPreReportingData();

        // Validate response
        if (!response) {
            throw new Error('No response from pre-reporting API');
        }

        if (!response.success) {
            throw new Error(`API Error: ${response.error || 'Unknown error'}`);
        }

        if (!response.data || response.data.length === 0) {
            console.log('[UI-COORDINATOR] No pre-reporting data available');
            this.updateUIForNoData();
            return { success: true, message: 'No data available' };
        }

        // Load and initialize the UI
        await this.initializeInteractiveUI(response.data, response.sessionId);

        return { 
            success: true, 
            message: `UI loaded successfully from ${source}`,
            recordCount: response.data.length,
            sessionId: response.sessionId
        };
    }

    async initializeInteractiveUI(data, sessionId) {
        console.log(`[UI-COORDINATOR] Initializing interactive UI with ${data.length} records...`);

        // Find the container
        const container = this.findUIContainer();
        if (!container) {
            throw new Error('UI container not found');
        }

        // Load InteractivePreReporting if needed
        if (!window.InteractivePreReporting) {
            console.log('[UI-COORDINATOR] Loading InteractivePreReporting class...');
            // The script should already be loaded in index.html
            await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
            
            if (!window.InteractivePreReporting) {
                throw new Error('InteractivePreReporting class not available');
            }
        }

        // Clear any existing UI
        container.innerHTML = '<div class="loading-message">🎨 Initializing interactive interface...</div>';

        // Create and initialize the interactive UI
        console.log('[UI-COORDINATOR] Creating InteractivePreReporting instance...');
        window.interactivePreReporting = new window.InteractivePreReporting(container, data);
        
        // Initialize the UI
        if (typeof window.interactivePreReporting.initialize === 'function') {
            await window.interactivePreReporting.initialize();
        } else {
            // Fallback to direct rendering
            await window.interactivePreReporting.processAndRender();
        }

        console.log('[UI-COORDINATOR] Interactive UI initialized successfully');
    }

    findUIContainer() {
        // Try multiple possible container IDs
        const possibleContainers = [
            'pre-reporting-interactive-ui',
            'pre-reporting-container', 
            'processing-pre-reporting-container',
            'pre-reporting-content'
        ];

        for (const id of possibleContainers) {
            const container = document.getElementById(id);
            if (container) {
                console.log(`[UI-COORDINATOR] Found UI container: ${id}`);
                return container;
            }
        }

        console.error('[UI-COORDINATOR] No UI container found');
        return null;
    }

    updateUIForNoData() {
        const container = this.findUIContainer();
        if (container) {
            container.innerHTML = `
                <div class="no-data-message">
                    <h3>✅ Audit Completed Successfully</h3>
                    <p>No changes detected in this payroll audit.</p>
                </div>
            `;
        }
    }

    shouldRetry(error) {
        const retryableErrors = [
            'database is locked',
            'Process exited',
            'SIGTERM',
            'Connection failed'
        ];

        return retryableErrors.some(errorType => 
            error.message && error.message.includes(errorType)
        );
    }

    processQueue() {
        // For now, we don't queue requests - we just prevent conflicts
        // Future enhancement could add intelligent queuing
        console.log('[UI-COORDINATOR] Processing queue (currently no queuing implemented)');
    }

    getStatus() {
        return {
            isLoading: this.isLoading,
            lastLoadTime: this.lastLoadTime,
            queueLength: this.loadingQueue.length
        };
    }
}

// Global instance
let globalUICoordinator = null;

function getUILoadingCoordinator() {
    if (!globalUICoordinator) {
        globalUICoordinator = new UILoadingCoordinator();
    }
    return globalUICoordinator;
}

// Export for both browser and Node.js environments
if (typeof window !== 'undefined') {
    // Browser environment
    window.UILoadingCoordinator = UILoadingCoordinator;
    window.getUILoadingCoordinator = getUILoadingCoordinator;
    console.log('[UI-COORDINATOR] Loaded in browser environment');
} else if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = { UILoadingCoordinator, getUILoadingCoordinator };
}
