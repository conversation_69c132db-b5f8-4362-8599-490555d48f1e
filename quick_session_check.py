#!/usr/bin/env python3
"""Quick check of current session after successful audit"""

import sqlite3

def quick_session_check():
    """Quick check of session status"""
    print("🔍 QUICK SESSION CHECK")
    print("=" * 30)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if result:
            session_id = result[0]
            print(f"Current session: {session_id}")
            
            # Check data counts
            cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session_id,))
            extracted = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            comparison = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session_id,))
            pre_reporting = cursor.fetchone()[0]
            
            print(f"Extracted: {extracted}")
            print(f"Comparison: {comparison}")
            print(f"Pre-reporting: {pre_reporting}")
            
            if pre_reporting > 0:
                print("✅ SUCCESS: Pre-reporting data available!")
                print("💡 Refresh your app - UI should work now")
            else:
                print("❌ No pre-reporting data")
        else:
            print("❌ No current session")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    quick_session_check()
