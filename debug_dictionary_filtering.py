#!/usr/bin/env python3
"""Debug dictionary filtering for external loans"""

import sqlite3

def debug_dictionary_filtering():
    """Debug why external loans are filtered out by dictionary"""
    print("🔍 DEBUGGING DICTIONARY FILTERING FOR EXTERNAL LOANS")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        
        if not result:
            print("❌ No current session")
            return
            
        session_id = result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check external loan items and their dictionary status
        print(f"\n🔍 EXTERNAL LOAN ITEMS AND DICTIONARY STATUS:")
        
        cursor.execute('''
            SELECT cr.item_label, 
                   COUNT(*) as count,
                   di.item_name,
                   di.include_in_report,
                   di.include_new,
                   CASE 
                       WHEN di.item_name IS NULL THEN 'NO DICTIONARY ENTRY'
                       WHEN di.include_in_report = 1 AND di.include_new = 1 THEN 'ALLOWED'
                       WHEN di.include_in_report IS NULL AND di.include_new IS NULL THEN 'ALLOWED (NULL)'
                       ELSE 'FILTERED OUT'
                   END as status
            FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND cr.change_type = 'NEW' AND (
                cr.item_label LIKE '%GCB BANK%BALANCE B/F' OR
                cr.item_label LIKE '%OMNI BANK%BALANCE B/F' OR
                cr.item_label LIKE '%GLICO%BALANCE B/F' OR
                cr.item_label LIKE '%ECOBANK%BALANCE B/F'
            )
            GROUP BY cr.item_label, di.item_name, di.include_in_report, di.include_new
            ORDER BY cr.item_label
        ''', (session_id,))
        
        external_items = cursor.fetchall()
        if external_items:
            print("   External loan items:")
            for row in external_items:
                print(f"     {row[0]} ({row[1]} records)")
                print(f"       Dictionary entry: {row[2] if row[2] else 'NONE'}")
                print(f"       include_in_report: {row[3]}")
                print(f"       include_new: {row[4]}")
                print(f"       Status: {row[5]}")
                print()
        else:
            print("   ❌ No external loan items found")
        
        # Check what items WOULD be loaded by tracker feeding query
        print(f"\n🔍 ITEMS THAT PASS TRACKER FEEDING FILTER:")
        
        cursor.execute('''
            SELECT cr.item_label, COUNT(*) as count
            FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND cr.change_type = 'NEW'
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
              AND (di.include_new = 1 OR di.include_new IS NULL)
              AND (
                cr.item_label LIKE '%GCB BANK%' OR
                cr.item_label LIKE '%OMNI BANK%' OR
                cr.item_label LIKE '%GLICO%' OR
                cr.item_label LIKE '%ECOBANK%'
              )
            GROUP BY cr.item_label
            ORDER BY count DESC
        ''', (session_id,))
        
        filtered_items = cursor.fetchall()
        if filtered_items:
            print("   Items that pass filter:")
            for row in filtered_items:
                print(f"     {row[0]} - {row[1]} records")
        else:
            print("   ❌ NO external loan items pass the tracker feeding filter")
        
        # Check all dictionary entries for loan items
        print(f"\n🔍 ALL LOAN DICTIONARY ENTRIES:")
        
        cursor.execute('''
            SELECT item_name, include_in_report, include_new
            FROM dictionary_items
            WHERE item_name LIKE '%LOAN%' OR item_name LIKE '%ADVANCE%'
            ORDER BY item_name
        ''')
        
        loan_dict_entries = cursor.fetchall()
        if loan_dict_entries:
            print("   Loan dictionary entries:")
            for row in loan_dict_entries:
                print(f"     {row[0]} - report:{row[1]}, new:{row[2]}")
        else:
            print("   ❌ No loan dictionary entries found")
        
        # Check what the current tracker feeding query actually returns
        print(f"\n🔍 CURRENT TRACKER FEEDING QUERY RESULTS:")
        
        cursor.execute('''
            SELECT cr.item_label, COUNT(*) as count
            FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND cr.change_type = 'NEW'
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
              AND (di.include_new = 1 OR di.include_new IS NULL)
            GROUP BY cr.item_label
            HAVING cr.item_label LIKE '%LOAN%' OR cr.item_label LIKE '%BANK%'
            ORDER BY count DESC
            LIMIT 20
        ''', (session_id,))
        
        current_results = cursor.fetchall()
        if current_results:
            print("   Current tracker feeding results (loan/bank items):")
            for row in current_results:
                print(f"     {row[0]} - {row[1]} records")
        else:
            print("   ❌ No loan/bank items in current tracker feeding results")
        
        conn.close()
        
        print(f"\n💡 ANALYSIS:")
        print("1. External loans exist but may be filtered out by dictionary")
        print("2. Dictionary entries may be missing or have wrong flags")
        print("3. Need to either fix dictionary or adjust filter logic")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_dictionary_filtering()
