#!/usr/bin/env node
/**
 * Check Pre-Reporting Results Table Schema
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function checkPreReportingSchema() {
    console.log('🔍 CHECKING PRE_REPORTING_RESULTS TABLE SCHEMA');
    console.log('=' .repeat(60));
    
    try {
        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');
        const db = new sqlite3.Database(dbPath);
        
        // Get table schema
        const schema = await new Promise((resolve, reject) => {
            db.all("PRAGMA table_info(pre_reporting_results)", (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
        
        console.log('📊 PRE_REPORTING_RESULTS TABLE SCHEMA:');
        console.log('Column Name | Type | Not Null | Default | Primary Key');
        console.log('-'.repeat(60));
        
        schema.forEach(col => {
            console.log(`${col.name.padEnd(15)} | ${col.type.padEnd(10)} | ${col.notnull ? 'YES' : 'NO'} | ${col.dflt_value || 'NULL'} | ${col.pk ? 'YES' : 'NO'}`);
        });
        
        // Check if table exists and has data
        const count = await new Promise((resolve, reject) => {
            db.get("SELECT COUNT(*) as count FROM pre_reporting_results", (err, row) => {
                if (err) reject(err);
                else resolve(row ? row.count : 0);
            });
        });
        
        console.log(`\n📊 Total records in pre_reporting_results: ${count}`);
        
        // Show sample data if exists
        if (count > 0) {
            const sampleData = await new Promise((resolve, reject) => {
                db.all("SELECT * FROM pre_reporting_results LIMIT 3", (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                });
            });
            
            console.log('\n📋 SAMPLE DATA:');
            sampleData.forEach((row, i) => {
                console.log(`\nRecord ${i + 1}:`);
                Object.entries(row).forEach(([key, value]) => {
                    console.log(`  ${key}: ${value}`);
                });
            });
        }
        
        // Also check comparison_results schema for reference
        const comparisonSchema = await new Promise((resolve, reject) => {
            db.all("PRAGMA table_info(comparison_results)", (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
        
        console.log('\n📊 COMPARISON_RESULTS TABLE SCHEMA (for reference):');
        console.log('Column Name | Type | Not Null | Default | Primary Key');
        console.log('-'.repeat(60));
        
        comparisonSchema.forEach(col => {
            console.log(`${col.name.padEnd(15)} | ${col.type.padEnd(10)} | ${col.notnull ? 'YES' : 'NO'} | ${col.dflt_value || 'NULL'} | ${col.pk ? 'YES' : 'NO'}`);
        });
        
        db.close();
        
        return { preReportingSchema: schema, comparisonSchema };
        
    } catch (error) {
        console.error('❌ Error checking schema:', error);
        throw error;
    }
}

// Run the schema check
if (require.main === module) {
    checkPreReportingSchema().then(result => {
        console.log('\n✅ Schema check complete');
        process.exit(0);
    }).catch(error => {
        console.error('Schema check failed:', error);
        process.exit(1);
    });
}

module.exports = { checkPreReportingSchema };
